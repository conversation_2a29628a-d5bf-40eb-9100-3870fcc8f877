#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function parseTestResults(resultsPath) {
  try {
    // Read the results file
    const rawData = fs.readFileSync(resultsPath, 'utf8');
    const results = JSON.parse(rawData);
    
    // Extract key metrics
    const summary = {
      // Basic metrics
      totalTests: results.numTotalTests,
      passedTests: results.numPassedTests,
      failedTests: results.numFailedTests,
      passPercentage: results.numTotalTests > 0 
        ? Math.round((results.numPassedTests / results.numTotalTests) * 100) 
        : 0,
      
      // Test suite metrics
      totalSuites: results.numTotalTestSuites,
      passedSuites: results.numPassedTestSuites,
      failedSuites: results.numFailedTestSuites,
      
      // Execution info
      duration: results.endTime ? results.endTime - results.startTime : 0,
      success: results.success,
      
      // Failed test names (simplified)
      failedTestNames: [],
      
      // Passed test names (simplified)
      passedTestNames: [],
      
      // Per-file breakdown
      fileBreakdown: {}
    };
    
    // Extract test information per file
    if (results.testResults && Array.isArray(results.testResults)) {
      results.testResults.forEach(suite => {
        const fileName = suite.name ? path.basename(suite.name) : 'Unknown';
        
        // Initialize file breakdown
        if (!summary.fileBreakdown[fileName]) {
          summary.fileBreakdown[fileName] = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            failedTestNames: [],
            passedTestNames: []
          };
        }
        
        if (suite.assertionResults && Array.isArray(suite.assertionResults)) {
          suite.assertionResults.forEach(test => {
            summary.fileBreakdown[fileName].totalTests++;
            
            if (test.status === 'passed') {
              summary.fileBreakdown[fileName].passedTests++;
              summary.fileBreakdown[fileName].passedTestNames.push(test.title);
              summary.passedTestNames.push(test.title);
            } else if (test.status === 'failed') {
              summary.fileBreakdown[fileName].failedTests++;
              summary.fileBreakdown[fileName].failedTestNames.push(test.title);
              summary.failedTestNames.push(test.title);
            }
          });
        }
      });
    }
    
    return summary;
  } catch (error) {
    console.error('Error parsing test results:', error.message);
    return null;
  }
}

function formatMarkdownSummary(summary) {
  let markdown = '## 📊 Test Summary\n\n';
  
  // Overall metrics
  markdown += '### Overall Results\n';
  markdown += `- **Total Tests:** ${summary.totalTests}\n`;
  markdown += `- **Passed:** ${summary.passedTests} ✅\n`;
  markdown += `- **Failed:** ${summary.failedTests} ❌\n`;
  markdown += `- **Pass Rate:** ${summary.passPercentage}% ${summary.passPercentage >= 75 ? '🟢' : summary.passPercentage >= 50 ? '🟡' : '🔴'}\n\n`;
  
  // Per-file breakdown with collapsible section
  markdown += '<details>\n';
  markdown += '<summary><strong>📁 Per-File Breakdown</strong> (click to expand)</summary>\n\n';
  markdown += '| File | Total | Passed | Failed | Pass Rate |\n';
  markdown += '|------|-------|--------|--------|----------|\n';
  
  // Sort files alphabetically/numerically
  const sortedFiles = Object.entries(summary.fileBreakdown).sort(([a], [b]) => a.localeCompare(b, undefined, {numeric: true}));
  
  sortedFiles.forEach(([file, stats]) => {
    const passRate = stats.totalTests > 0 ? Math.round((stats.passedTests / stats.totalTests) * 100) : 0;
    markdown += `| ${file} | ${stats.totalTests} | ${stats.passedTests} | ${stats.failedTests} | ${passRate}% |\n`;
  });
  
  markdown += '\n</details>\n\n';
  
  // Failed tests list with collapsible section
  if (summary.failedTestNames.length > 0) {
    markdown += '<details>\n';
    markdown += `<summary><strong>❌ Failed Tests</strong> (${summary.failedTestNames.length} tests - click to expand)</summary>\n\n`;
    summary.failedTestNames.forEach((testName, index) => {
      markdown += `${index + 1}. ${testName}\n`;
    });
    markdown += '\n</details>\n';
  }
  
  return markdown;
}

function main() {
  // Get command line arguments
  const args = process.argv.slice(2);
  const resultsPath = args[0] || 'results.json';
  const outputPath = args[1] || 'test-summary.json';
  
  // Check if results file exists
  if (!fs.existsSync(resultsPath)) {
    console.error(`Results file not found: ${resultsPath}`);
    process.exit(1);
  }
  
  // Parse the results
  const summary = parseTestResults(resultsPath);
  
  if (!summary) {
    console.error('Failed to parse test results');
    process.exit(1);
  }
  
  // Write summary to file
  fs.writeFileSync(outputPath, JSON.stringify(summary, null, 2));
  
  // Print formatted output for GitHub Actions
  console.log('\n' + '='.repeat(60));
  console.log('TEST EXECUTION SUMMARY');
  console.log('='.repeat(60) + '\n');
  
  console.log(`Total Tests: ${summary.totalTests}`);
  console.log(`✅ Passed: ${summary.passedTests} (${summary.passPercentage}%)`);
  console.log(`❌ Failed: ${summary.failedTestNames.length}`);
  console.log(`⏱️  Duration: ${(summary.duration / 1000).toFixed(2)}s`);
  
  console.log('\n' + '-'.repeat(60));
  console.log('PER-FILE BREAKDOWN');
  console.log('-'.repeat(60));
  
  // Sort files alphabetically/numerically
  const sortedFileBreakdown = Object.entries(summary.fileBreakdown).sort(([a], [b]) => a.localeCompare(b, undefined, {numeric: true}));
  
  sortedFileBreakdown.forEach(([file, stats]) => {
    const passRate = stats.totalTests > 0 ? Math.round((stats.passedTests / stats.totalTests) * 100) : 0;
    console.log(`\n📄 ${file}`);
    console.log(`   Total: ${stats.totalTests} | Passed: ${stats.passedTests} | Failed: ${stats.failedTests} | Pass Rate: ${passRate}%`);
    
    if (stats.failedTestNames.length > 0) {
      console.log('   Failed Tests:');
      stats.failedTestNames.forEach((test, i) => {
        console.log(`     ${i + 1}. ${test}`);
      });
    }
  });
  
  if (summary.failedTestNames.length > 0) {
    console.log('\n' + '-'.repeat(60));
    console.log('FAILED TESTS');
    console.log('-'.repeat(60));
    summary.failedTestNames.forEach((test, index) => {
      console.log(`${index + 1}. ${test}`);
    });
  }
  
  console.log('\n' + '='.repeat(60) + '\n');
  
  // Write markdown summary for GitHub Actions
  const markdownPath = outputPath.replace('.json', '.md');
  fs.writeFileSync(markdownPath, formatMarkdownSummary(summary));
  
  console.log(`Summary written to: ${outputPath}`);
  console.log(`Markdown summary written to: ${markdownPath}`);
}

// Run the script
if (require.main === module) {
  main();
} 
