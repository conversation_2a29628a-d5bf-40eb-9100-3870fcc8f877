# Free-Floating Car-Share API - Product Requirements Document

## 1. Overview

The Free-Floating Car-Share API provides a comprehensive platform for managing distributed vehicle fleets across metropolitan areas. This system enables operators to deploy and maintain shared vehicles that renters can access on-demand without traditional reservation constraints or fixed return locations.

At its core, the platform orchestrates the complex interactions between three primary user types: renters who utilize vehicles for short-term transportation needs, fleet operators who manage the overall system health and zone configurations, and mechanics who ensure vehicle readiness through maintenance and service workflows.

The system employs a zone-based architecture where parking areas are defined with target vehicle capacities, enabling automatic detection of supply imbalances and corresponding operational responses. Vehicle states transition through well-defined lifecycles from available to in-use, maintenance, or retirement, with each transition governed by business rules that ensure fleet integrity and user safety. A sophisticated ledger system tracks zone balances, vehicle mileage, and renter reputation scores, providing the foundational data layer for operational decisions and access controls.

This API defines the complete interface for building client applications that interact with the car-share platform, whether for consumer-facing rental experiences, operator dashboards, or maintenance management tools. The design emphasizes operational flexibility while maintaining strict invariants around vehicle safety, user accountability, and system consistency.

## 2. Global Specifications

This section defines system-wide rules, principles, and standards that apply to the entire API. All endpoint behaviors are constrained by the specifications detailed herein.

### 2.1 Authentication & User Context

User context is determined by two mandatory HTTP request headers for all endpoints:
* `X-User-ID`: (string) The unique identifier of the user making the request. This ID is used for authorization checks and data association. It must be a non-empty string conforming to the pattern `user_[0-9A-HJKMNP-TV-Z]{26}`.
* `X-User-Role`: (string) The role assigned to the user. Must be one of the following values: `RENTER`, `FLEET_OPERATOR`, `MECHANIC`.

Failure to provide valid, non-empty headers will result in a `400 Bad Request` with `error_id: ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER`. The system assumes these headers have been validated by an upstream gateway and trusts their content for authorization logic.

### 2.2 Standard Response Formats

#### **Standard Success Response Format**

All successful API responses (those with 2xx status codes) follow this standard envelope format in their JSON body:

```json
{
  "meta": {
    "api_request_id": "req_RANDOMALPHANUM123456",
    "api_request_timestamp": "2025-05-24T10:30:00.123Z"
  },
  "response_type": "object" | "array",
  "data": { }
}
```

* The `api_request_id` is a server-generated unique identifier for the request.
* The `api_request_timestamp` is a server-generated ISO 8601 UTC timestamp.
* The `response_type` is either "object" for a single resource or "array" for a list of resources.
* The `data` field contains the resource object or an array of resource objects. For 2xx responses with no data, it contains an empty object.


#### **Standard Error Response Format**

All error API responses (4xx status codes) follow this standard envelope format:

```json
{
  "meta": {
    "api_request_id": "req_RANDOMALPHANUM123456",
    "api_request_timestamp": "2025-05-24T10:30:00.123Z"
  },
  "response_type": "error",
  "data": {
    "error_id": "UNIQUE_ERROR_IDENTIFIER_STRING",
    "message": "A human-readable explanation of the error."
  }
}
```

* The `error_id` is a unique, machine-readable string that identifies the specific error.
* The `message` provides a human-readable explanation of what went wrong.


### 2.3 Core Systems

The platform's logic is underpinned by four container-scoped systems that track critical state across multiple domains. These systems are not directly mutable via dedicated endpoints but are updated as side effects of other business workflows.

* **Fleet Action Counter:** This system maintains a monotonically increasing integer counter for each `CityFleet`, initialized at 0 when the fleet is created. The counter increments by 1 upon successful completion of any state-modifying API request (POST, PATCH, DELETE operations that return a 2xx status code). Read-only GET requests do not affect the counter. This counter serves as the fleet's logical clock for determining rental durations and late returns in a deterministic manner.
* **Zone Balance Ledger:** This system maintains a real-time count of the vehicle surplus or deficit for every Parking Zone. It is the core driver of dynamic zone availability.
* **Vehicle Mileage Ledger:** This system tracks the distance each vehicle has traveled since its last maintenance event. It is essential for determining service needs and managing maintenance overrides.
* **Renter Reputation Ledger:** This ledger manages the standing of each renter in the system, tracking a score that governs privileges and a history of infractions that trigger penalties.

### 2.4 System Invariants

These are universal truths that the system must uphold at all times, across all operations. All API logic is subordinate to these invariants.

* **Vehicle Odometer Integrity:** A vehicle's `odometer_mi` field must be strictly monotonically increasing. Any operation that attempts to set this value to less than its current value is invalid.
* **Rental Uniqueness:** A `RENTER` may have at most one `active_rental_id` at any given time. This field on the User resource must either be `null` or point to a valid Rental resource that is not in a terminal state (`COMPLETED`, `CANCELLED`).
* **Ledger Association:** Every `RENTER`-role User must have a corresponding record in the Renter Reputation Ledger. Every Vehicle must have a corresponding record in the Vehicle Mileage Ledger. Every Parking Zone must have a corresponding record in the Zone Balance Ledger.
* **Vehicle Location Exclusivity:** A Vehicle's `current_zone_id` must either be `null` or point to a valid Parking Zone. A `null` value is only permissible when the vehicle's `state` is `IN_USE`, signifying it is in transit during an active rental. When a vehicle is associated with a zone, its presence is reflected as a +1 credit to that zone's `surplus` in the Zone Balance Ledger. When a vehicle begins a rental and leaves a zone, its departure is reflected as a -1 debit.
* **Fleet Containment:** All resources, including Users, Vehicles, and Parking Zones, must belong to a parent `CityFleet`. Operations may not cross fleet boundaries.

### 2.4.1 Hierarchy of Truth

In cases of conflicting rules or when multiple validations fail simultaneously, the following order of precedence dictates which error is returned:

1.  **User Suspension:** A `suspended` status on a `User` resource overrides all other permissions. A suspended user cannot initiate any rental activity.
2.  **Probationary Fleet Alert Restriction:** An attempt by a `PROBATIONARY` renter to quote a rental during a `HIGH_ALERT` fleet `phase_state` is rejected before any other rental-specific checks are made.
3.  **Vehicle State:** The `Vehicle.state` is absolute. A vehicle that is not `AVAILABLE` (or `NEEDS_MAINTENANCE` with a valid override) cannot be rented, regardless of any other condition.
4.  **Manual Zone Lock:** A `manual_lock: true` flag on a `ParkingZone` resource takes precedence over any automatic lock state adjustments triggered by the `ZoneBalanceLedger`.
5.  **Return Validation Precedence:** During vehicle return, validation checks are performed in a specific order. The system first checks for a `HARD_LOCK` on the destination zone. If that check passes, it then validates the odometer reading. An error is returned for the first validation that fails.
6.  **Hard Zone Lock:** A `lock_state` of `HARD_LOCK` on a `ParkingZone` unconditionally prevents vehicle returns to that zone.
7.  **Ledger-Based Restrictions:** Rules based on ledger values (e.g., `RenterReputationLedger` score, `ZoneBalanceLedger` surplus) are evaluated after the higher-precedence states are confirmed to be permissive.

## 3. Business Logic & Workflows

### 3.1 Fleet and Zone Management

The foundation of the service is the `CityFleet`, which acts as the top-level container for all operations in a specific metropolitan area. A `FLEET_OPERATOR` can establish a new fleet by providing a `name`, which must be a string between 3 and 50 characters, and a `city_code`, a 2 or 3-letter uppercase string representing the region. The fleet must also be configured with an `allowed_vehicle_classes` array, specifying which types of vehicles, such as `ECONOMY` or `EV`, can be registered within it.

Within a fleet, a `FLEET_OPERATOR` defines `ParkingZone` resources. Each zone requires a `display_name` (3-32 characters) and a unique `code`, which is a string following the format `^[A-Z]{3}-\d{2}$`. Codes beginning with "EA-" are reserved for future use and will be rejected with `error_id: ERR_RESERVED_ZONE_CODE`. The zone's physical area is defined by a `geojson` string, representing a polygon with no more than 50 vertices. A critical operational parameter is `target_vehicles`, an integer from 0 to 500 that specifies the ideal number of vehicles for that zone.

The operational status of a zone is governed by its `lock_state`, an automated system response to vehicle availability. This state is derived from the Zone Balance Ledger's `surplus` value. If the surplus drops to -5 or lower, the zone enters a `SOFT_LOCK`. If it further degrades to -10 or lower, it enters a `HARD_LOCK`. As vehicles return and the surplus recovers above the `SOFT_LOCK` threshold, the lock state automatically transitions back to `OPEN`. However, the `HARD_LOCK` state is persistent and does not automatically resolve. It must be manually cleared by a `FLEET_OPERATOR` following the two-step process detailed in Section 4.2: first, an explicit request to set the `lock_state` to `OPEN`, which also engages the `manual_lock`; second, a subsequent request to set `manual_lock` to `false` to resume automated control. To allow for such operational intervention, each zone includes a `manual_lock` boolean field which defaults to `false`. An operator can override the automated system at any time by manually setting the `lock_state` via the endpoint described in Section 4.2, an action that simultaneously sets the `manual_lock` field to `true`. This manual lock must be explicitly released by sending `manual_lock: false` before automatic adjustments can resume.

The fleet itself has a global `phase_state`. If three or more zones within the fleet enter a `HARD_LOCK` simultaneously, the entire fleet's `phase_state` transitions to `HIGH_ALERT`, which triggers system-wide pricing adjustments as described in Section 3.4. The `phase_state` returns to `NORMAL` once the number of zones in `HARD_LOCK` is two or fewer.

To help manage fleet distribution, a `FLEET_OPERATOR` can optionally configure zones to encourage or discourage the parking of certain vehicle classes. Each `ParkingZone` resource may contain two optional array fields: `incentivized_classes` and `discouraged_classes`. These fields, which default to empty, influence rental pricing and renter reputation.

### 3.2 Vehicle Lifecycle & Maintenance

Vehicles are the core assets of the fleet. A `MECHANIC` registers a new vehicle by providing its `vin` (a 17-character identifier), `license_plate` (2-8 alphanumeric characters), its `class` (e.g., `LUXURY`), which must be present in the fleet's `allowed_vehicle_classes` list or the request will be rejected with `error_id: ERR_VEHICLE_CLASS_NOT_ALLOWED_IN_FLEET`, its initial `odometer_mi`, and its `service_interval_mi`. The vehicle's `last_service_odometer_mi` must also be provided and, for a new vehicle, is expected to be equal to the initial `odometer_mi`. Upon creation, a vehicle must be assigned to a `current_zone_id`, which must reference a valid zone within the fleet or the request will fail with `error_id: ERR_ZONE_NOT_FOUND`; failure to provide the `current_zone_id` field itself will result in `error_id: ERR_VEHICLE_MUST_HAVE_ZONE`. For vehicles where the `class` is `EV`, a `battery_pct` integer field (0-100) is mandatory.

A vehicle's operational readiness is captured by its `state`. The primary rentable state is `AVAILABLE`. A complex set of rules governs its transitions, detailed throughout this document. One key factor is maintenance. Each vehicle has a `service_interval_mi`, an integer between 1000 and 20000, dictating how many miles it can travel between services. The system calculates a derived, readonly field `miles_until_service` by subtracting the miles driven since the last service (i.e., `odometer_mi` - `last_service_odometer_mi`) from the `service_interval_mi`. If this calculated value is greater than zero, the vehicle is considered to have sufficient service range. If the value is zero or less, the vehicle is not rentable unless a maintenance override is granted.

A `MECHANIC` can place a `MaintenanceHold` on a vehicle for a specific `reason_code`. Upon creation, a hold is given an `active` status, a boolean field which defaults to `true`. The `reason_code` must be one of the following string values: `OIL_CHANGE`, `BRAKE_INSPECT`, `TIRE_ROTATION`, or `OTHER`. This action also adds a `MAINT_HOLD` string to the vehicle's `flags` array, which is a list that can contain up to 10 distinct string values. When a hold's `active` status is `true`, the vehicle is generally not rentable. However, to create an exception, a `MECHANIC` may set an `override_allowed` boolean field to `true` and specify a number of `allowed_miles_remaining`, which is an integer between 0 and 200. This creates a specific "escape hatch" detailed in Section 3.3. When a `MECHANIC` reports that an EV has been charged via the appropriate endpoint (see Section 4.2), its `battery_pct` field is set to 100. Attempting to call this endpoint for a vehicle whose `class` is not `EV` must be rejected with `error_id: ERR_VEHICLE_NOT_EV`.

When a `MECHANIC` performs service, they reset the vehicle's mileage clock. This action is only valid if the vehicle is in the `NEEDS_MAINTENANCE` state; otherwise, it must be rejected with `ERR_VEHICLE_NOT_IN_MAINTENANCE`. A successful service action transitions the vehicle to `AVAILABLE`, unless a `CLEAN_REQ` flag is also present, in which case it transitions to `NEEDS_CLEANING`. Similarly, a `FLEET_OPERATOR` can confirm that a vehicle has been cleaned via the endpoint in Section 4.2. This action is only valid if the vehicle is in the `NEEDS_CLEANING` state; otherwise, it results in the `ERR_VEHICLE_NOT_DIRTY` error. A successful cleaning removes the `CLEAN_REQ` flag and updates its state. The final state transitions to `NEEDS_MAINTENANCE` if the vehicle's `miles_until_service` is zero or less or if a `MAINT_HOLD` flag is present; otherwise, it transitions to `AVAILABLE`.

Finally, a `FLEET_OPERATOR` can permanently retire a vehicle, moving it to the terminal `RETIRED` state, from which it cannot be recovered.

### 3.3 Renter Conduct & Privileges

Users are created by providing a `role`, a `name` as a string between 2 and 40 characters, and a valid `email` address. A `phone` number may optionally be provided in E.164 format. For those with the `RENTER` role, their behavior is tracked in the Renter Reputation Ledger, which maintains their `reputation_score`, an integer between 0 and 300, initialized to 100. This score determines certain privileges. For example, a renter can only operate `LUXURY` class vehicles if their score is 150 or greater; attempting to rent one with a lower score must be rejected with `ERR_LUXURY_LOCK`. This privilege is reflected in a readonly boolean `luxury_unlock` field on their user profile.

A renter's standing can be negatively impacted by their actions. The most common issue is a late return. The system tracks a `late_counter` in the reputation ledger. The consequences of a late return escalate based on this counter.
* **First offense:** 5-point reputation loss and a small surcharge.
* **Second and third offense:** 15-point reputation loss and a medium surcharge.
* **Fourth and subsequent offenses:** 30-point reputation loss, a large surcharge, and the renter's `suspended` flag is set to `true`.

A suspended renter is barred from all new rental activities. Specifically, a user with the `suspended` flag set to `true` cannot request a quote or confirm a new rental. They can, however, complete an in-progress rental by returning the vehicle. A `FLEET_OPERATOR` must manually reset the `suspended` flag to `false` to restore their privileges. The user's profile includes a `late_return_history`, a readonly array containing the IDs of their last five late rentals.

Furthermore, the Renter Reputation Ledger governs a renter's overall account standing. If a renter's `reputation_score` falls below 75 at any point, their account status immediately transitions to `PROBATIONARY`. This status is automatically cleared only when their score rises back to 85 or above. While a renter is in the `PROBATIONARY` state, they are subject to stricter operational constraints designed to encourage safer driving and vehicle management:
* **Fleet Alert Restriction:** A `PROBATIONARY` renter cannot initiate a new rental quote if the fleet's `phase_state` is currently `HIGH_ALERT`. Any such attempt must be rejected with the error `ERR_PROBATIONARY_RESTRICTION_HIGH_ALERT`. This check is performed after the user suspension check but before the vehicle state check in the hierarchy of truth.
* **Reduced Rental Duration:** The `quoted_action_duration` for any rental quoted by a `PROBATIONARY` renter is calculated using a more stringent formula: `10 + floor(distance_estimate_mi / 5)`. This gives them a smaller window to complete their trip before it is considered late.

In exceptional circumstances, a renter with a `reputation_score` greater than 120 can rent a vehicle that is in the `NEEDS_MAINTENANCE` state. This is only permissible if there is exactly one active `MaintenanceHold` resource associated with the vehicle, and that hold has its `override_allowed` property set to `true`. The check for the number of active holds is performed against the `MaintenanceHold` resources themselves, not the vehicle's flags. If the vehicle has multiple active holds, or a single hold that is not overridable, the rental is forbidden, triggering the `ERR_AMBIGUOUS_OVERRIDE_HOLD` or `ERR_HOLD_NOT_OVERRIDABLE` error respectively. For a valid override, the renter must specify their `distance_estimate_mi`, and this value must not exceed the hold's `allowed_miles_remaining`; if it does, the request is rejected with `ERR_INSUFFICIENT_MAINTENANCE_OVERRIDE_MILES`. If successful, a `MAINT_OVERRIDE_GRANTED` flag is temporarily added to the vehicle's `flags` array, and a corresponding `override_miles_credit` equal to the `distance_estimate_mi` is recorded in the Vehicle Mileage Ledger. This entire override workflow is handled through the standard quoting process (Section 4.3).

### 3.4 The Rental Process

The rental process is a multi-step workflow initiated by a `RENTER`.

**1. Quoting:** The process begins with a request for a quote. A renter cannot have more than one active quote at a time; attempting to create a second will result in `error_id: ERR_QUOTE_EXISTS`. The renter specifies the `vehicle_id`, `start_zone_id`, `end_zone_id`, and an estimated mileage. The system first validates renter eligibility, ensuring the user is not suspended (which must be rejected with `error_id: ERR_USER_SUSPENDED`) and does not have an `active_rental_id` (per the `Rental Uniqueness` invariant, Section 2.4), which would result in `error_id: ERR_ACTIVE_RENTAL_EXISTS`. It then determines vehicle rentability. Rentability is not a static property of the vehicle but is evaluated dynamically based on the context of the quote. A vehicle is considered rentable if it meets one of two conditions: either its state is `AVAILABLE`, it has sufficient charge if it is an `EV` (at least 20% `battery_pct`), and its `miles_until_service` is greater than zero; or it qualifies for the maintenance override exception detailed in Section 3.3. If a vehicle does not meet these criteria, it is not rentable, and the quote request must be rejected with `error_id: ERR_VEHICLE_NOT_RENTABLE`. If a renter attempts the override without a sufficient `reputation_score`, the request must fail with `ERR_REPUTATION_LOW`. Furthermore, the vehicle's `current_zone_id` must match the quote's `start_zone_id` (a mismatch results in `error_id: ERR_VEHICLE_ZONE_MISMATCH`), and that zone's `lock_state` must be `OPEN` (otherwise, the request is rejected with `error_id: ERR_ZONE_LOCKED_FOR_RENTAL`). If all checks pass, the system calculates a `price_cents`. This price is influenced by the vehicle class and the surplus values of the start and end zones. If the fleet's `phase_state` is `HIGH_ALERT`, a 15% surcharge is added to this calculated price. Additionally, if the vehicle's `class` is listed in the `start_zone_id`'s `discouraged_classes` array, a separate 5% "relocation incentive" surcharge is also added to the final `price_cents`. These surcharges can be cumulative. The system also calculates a `quoted_action_duration`, an integer representing the number of Fleet Action Counter increments allotted for the rental. This duration is determined by the formula: `quoted_action_duration = 20 + floor(distance_estimate_mi / 4)`. However, if the renter is in a `PROBATIONARY` state (as described in Section 3.3), a more stringent formula is used: `quoted_action_duration = 10 + floor(distance_estimate_mi / 5)`. A `price_hash` (SHA-256) is also generated to prevent tampering.

A renter may also choose to cancel an active quote before confirmation, as described in Section 4.3. This action permanently invalidates the quote, allowing the renter to request a new one.

**2. Confirmation:** The renter confirms the rental by submitting the `quote_id` (which, if missing, results in `ERR_MISSING_QUOTE_ID`) and the exact `quoted_price_cents` from the quote. The system validates the submitted price against the stored `price_hash` for the quote; a mismatch indicates tampering and must be rejected with `error_id: ERR_QUOTE_TAMPER`. The system re-validates that the vehicle is still rentable. Upon success, the `Vehicle` state transitions to `RESERVED`, the `Rental` resource is created in the `CONFIRMED` state, and the renter's `active_rental_id` is set.

**3. Activation:** The rental becomes active when the renter unlocks the car. This action is only valid for a rental in the `CONFIRMED` state; otherwise, it fails with `ERR_RENTAL_NOT_CONFIRMED`. At the moment of activation, the system must record the vehicle's current `odometer_mi` and store it as the `start_odometer_mi` on the `Rental` resource. Additionally, the system captures the current value of the Fleet Action Counter and stores it as the `start_action_count` on the `Rental` resource, establishing the baseline for determining rental duration. The action then transitions the `Rental` state to `ACTIVE` and the `Vehicle` state to `IN_USE`, and sets the vehicle's `current_zone_id` to `null`. It is at this point that the vehicle's departure is registered, and the Zone Balance Ledger for the `start_zone_id` is debited.

**4. Return:** To complete the rental, the renter provides the `end_odometer_mi`, `end_zone_id`, and details for a post-trip inspection. These details are used to create a new `ConditionReport` (see Section 3.5), and the ID of this report is stored in the `Rental`'s `return_condition_report_id` field. The report includes a `cleanliness_grade` and `damage_grade`. The system's first priority is to validate the destination, checking that the provided `end_zone_id` corresponds to an existing zone (rejecting with `ERR_ZONE_NOT_FOUND` if not) and that the zone is not in a `HARD_LOCK` state (rejecting with `ERR_ZONE_LOCKED_FOR_RETURN` if it is). It then validates that the `end_odometer_mi` is not less than the `start_odometer_mi`, enforcing the `Vehicle Odometer Integrity` invariant (Section 2.4) and rejecting any attempt at rollback with `error_id: ERR_ODOMETER_ROLLBACK`. The system calculates the miles driven and determines if the rental is late by reading the current Fleet Action Counter value. A rental is considered late if the current counter value exceeds the sum of the rental's `start_action_count` and `quoted_action_duration` (i.e., `current_fleet_action_counter > start_action_count + quoted_action_duration`). If the rental was initiated under a maintenance override, the `override_miles_credit` from the Vehicle Mileage Ledger is subtracted from the miles driven before the result is used to update the ledger; the credit is then reset to zero. Based on the outcome, penalties or rewards are applied to the renter's reputation as described in Section 3.3. After processing any late-return penalties, the system evaluates zone-based incentives. If the returned vehicle's `class` is listed in the `end_zone_id`'s `incentivized_classes` array, the renter is rewarded with a **5-point bonus** to their `reputation_score`. Conversely, if the vehicle's `class` is listed in the `end_zone_id`'s `discouraged_classes` array, the renter receives a **5-point penalty** to their `reputation_score`. These adjustments are applied to the Renter Reputation Ledger immediately. The vehicle's `odometer_mi` is updated, and its `current_zone_id` is set to the `end_zone_id`. This event credits the Zone Balance Ledger for the destination zone. Finally, the vehicle's state is transitioned. If the rental was initiated under a maintenance override, the `MAINT_OVERRIDE_GRANTED` flag is removed from the vehicle, the `MaintenanceHold` that permitted the override has its `active` status set to `false` (which also removes the `MAINT_HOLD` flag from the vehicle), and the vehicle's state returns to `NEEDS_MAINTENANCE`. Otherwise, the state is transitioned based on an established order of precedence in the return conditions. Any condition requiring maintenance (a `MAJOR` `damage_grade` or a depleted service interval) is evaluated first and will move the vehicle to `NEEDS_MAINTENANCE`. If no maintenance is required, a `POOR` `cleanliness_grade` will then move it to `NEEDS_CLEANING` and add the `CLEAN_REQ` flag to its `flags` array. If the vehicle requires neither maintenance nor cleaning, its state transitions to `AVAILABLE`. If the return was late or involved damage, the `Rental` state becomes `INFRACTION_REVIEW`, requiring operator intervention to close. In this case, the renter's `active_rental_id` remains associated with the rental, which, in accordance with the `Rental Uniqueness` invariant (Section 2.4), effectively prevents the renter from initiating any new rentals until the review is complete. Otherwise, the rental moves to `COMPLETED` and the renter's `active_rental_id` is cleared.

### 3.5 Condition Reporting

A user can file a `ConditionReport` at any time for any vehicle. The `report_type` field indicates the context: a `RETURN` report is generated as part of the rental return workflow (see Section 3.4); a `SPOT` report can be filed by any user associated with the vehicle's parent `CityFleet` who observes a vehicle's condition in a zone; and a `MAINT_CHECK` is filed by a `MECHANIC`. The report requires assessing the vehicle's exterior and interior. For this, the `damage_grade` must be specified as one of three possible values: `NONE` for no issues, `MINOR` for small cosmetic problems, or `MAJOR` for issues that affect vehicle safety or operation. Similarly, the `cleanliness_grade` must be rated on a four-point scale: `EXCELLENT`, `GOOD`, `FAIR`, or `POOR`. If any report is filed with a `damage_grade` of `MAJOR`, the vehicle's `state` is immediately set to `NEEDS_MAINTENANCE`, overriding its current state unless it is `RETIRED`. If the vehicle was in a `RESERVED` state, the associated `Rental` is automatically transitioned to the `CANCELLED` state, and the renter's `active_rental_id` is cleared.

## 4. Endpoint Definitions

This section provides a skeletal map of the API surface. All detailed logic, validation, and side effects are described in Section 3.

### 4.1 Fleet & Resource Management Endpoints

#### POST /city-fleets
Creates a new CityFleet container for all subsequent resources.

#### GET /city-fleets/{fleet_id}
Retrieves the full details of a specific CityFleet container.

#### POST /city-fleets/{fleet_id}/users
Creates a new user account (Renter, Fleet Operator, or Mechanic).

#### POST /city-fleets/{fleet_id}/parking-zones
Creates a new parking zone within the specified fleet.

#### POST /city-fleets/{fleet_id}/vehicles
Registers a new vehicle within the specified fleet. This action is restricted to users with the `MECHANIC` role.

#### POST /city-fleets/{fleet_id}/maintenance-holds
Places a new maintenance hold on a specified vehicle.

#### POST /city-fleets/{fleet_id}/condition-reports
Allows a user to file a report on a vehicle's condition.

### 4.2 Operational Endpoints

#### GET /city-fleets/{fleet_id}/summary
Provides a role-dependent summary of fleet status or renter standing.

#### PATCH /city-fleets/{fleet_id}/users/{user_id}
Allows a `FLEET_OPERATOR` to update a `RENTER`'s status, such as lifting a suspension by setting the `suspended` flag to `false`. This endpoint cannot be used to modify users with other roles; such an attempt will result in `error_id: ERR_CANNOT_MODIFY_NON_RENTER`. The request body may only contain fields that are permissible to update (e.g., `suspended`); including other fields will result in `error_id: ERR_INVALID_UPDATE_FIELDS`. A request by a user whose role is not `FLEET_OPERATOR` will be rejected with `error_id: ERR_INVALID_ROLE`.

#### PATCH /city-fleets/{fleet_id}/parking-zones/{zone_id}
Allows a Fleet Operator to manually configure a zone's settings. A request may update either the `target_vehicles` field or the zone's lock configuration, but not both. The workflow to manually control a zone is a two-step process: first, an operator initiates a manual override by sending a request containing a `lock_state` (e.g., `{"lock_state": "OPEN"}`). This action sets the zone's `lock_state` to the provided value and simultaneously sets its `manual_lock` flag to `true`, pausing all automatic adjustments. To release the override and return the zone to automated control, the operator must send a second, distinct request containing `{"manual_lock": false}`. Sending `target_vehicles` along with either `lock_state` or `manual_lock` in the same request will result in the `ERR_AMBIGUOUS_ZONE_UPDATE` error.


#### POST /city-fleets/{fleet_id}/vehicles/{vehicle_id}/retire
Allows a Fleet Operator to permanently decommission a vehicle.

#### PATCH /city-fleets/{fleet_id}/vehicles/{vehicle_id}/service-status
Allows a Fleet Operator to mark a vehicle as cleaned.

#### POST /city-fleets/{fleet_id}/vehicles/{vehicle_id}/service
Allows a Mechanic to mark a vehicle as serviced, resetting maintenance clocks.

#### POST /city-fleets/{fleet_id}/vehicles/{vehicle_id}/charge
Allows a Mechanic to report that an EV's battery has been charged.

#### PATCH /city-fleets/{fleet_id}/maintenance-holds/{hold_id}/release
Allows a Mechanic to release an active maintenance hold. If the hold is already inactive, this action must fail with `ERR_HOLD_NOT_ACTIVE`. Otherwise, this action sets the hold's `active` status to `false` and removes the corresponding `MAINT_HOLD` flag from the vehicle's `flags` array.

### 4.3 Rental Workflow Endpoints

#### POST /city-fleets/{fleet_id}/rentals/quote
Allows a Renter to generate a price quote for a potential rental.

#### DELETE /city-fleets/{fleet_id}/rentals/quote/{quote_id}
Allows a Renter to cancel a quote they have not yet confirmed.

#### POST /city-fleets/{fleet_id}/rentals
Allows a Renter to confirm a quote, creating a rental reservation.

#### DELETE /city-fleets/{fleet_id}/rentals/{rental_id}
Allows a Renter to cancel a confirmed rental before it has been activated. This action is only valid if the rental is in the `CONFIRMED` state; attempting to cancel a rental in any other state (e.g., `ACTIVE`) must fail with `error_id: ERR_RENTAL_NOT_CANCELLABLE`. This action transitions the `Rental` state to `CANCELLED` and clears the renter's `active_rental_id`. If the rental was for a maintenance override, the `Vehicle` state returns to `NEEDS_MAINTENANCE` and the `MAINT_OVERRIDE_GRANTED` flag is removed from its `flags` array; otherwise, the vehicle state returns to `AVAILABLE`.

#### PATCH /city-fleets/{fleet_id}/rentals/{rental_id}
Allows a Renter to perform an action on a rental, specifically unlocking the vehicle.

#### PATCH /city-fleets/{fleet_id}/rentals/{rental_id}/return
Allows a Renter to return a vehicle, completing the rental. This action is only valid if the rental is in the `ACTIVE` state; otherwise, it must fail with `error_id: ERR_RENTAL_NOT_ACTIVE`.

#### PATCH /city-fleets/{fleet_id}/rentals/{rental_id}/review
Allows a Fleet Operator to resolve a rental that was flagged for an infraction. This action is only valid if the rental is in the `INFRACTION_REVIEW` state; otherwise, it must fail with `error_id: ERR_RENTAL_NOT_IN_REVIEW`. Upon successful review, the rental's state transitions to `COMPLETED`.


### 4.4 Data Retrieval Endpoints

#### GET /city-fleets/{fleet_id}/users/{user_id}
Retrieves the public profile of a user.

#### GET /city-fleets/{fleet_id}/parking-zones/{zone_id}
Retrieves details of a parking zone, with field-level redaction for Renters.

#### GET /city-fleets/{fleet_id}/vehicles/{vehicle_id}
Retrieves the full details of a specific vehicle.

#### GET /city-fleets/{fleet_id}/rentals/{rental_id}
Retrieves the details of a specific rental, with access limited by role.

#### GET /city-fleets/{fleet_id}/condition-reports/{report_id}
Retrieves a specific condition report.

#### GET /city-fleets/{fleet_id}/maintenance-holds
Lists active maintenance holds, with access and filtering varying by role.

#### GET /city-fleets/{fleet_id}/maintenance-holds/{hold_id}
Retrieves a specific maintenance hold.

#### GET /city-fleets/{fleet_id}/ledgers/zone-balance
Retrieves a snapshot of the Zone Balance Ledger (Fleet Operator only).

#### GET /city-fleets/{fleet_id}/ledgers/renter/{renter_id}/reputation
Retrieves the reputation ledger entry for a specific renter.

#### GET /city-fleets/{fleet_id}/ledgers/vehicle/{vehicle_id}/mileage
Retrieves the mileage ledger entry for a specific vehicle.

## 5. Error Code Catalogue

The generic `ERR_FORBIDDEN` code should only be used for authorization failures where a more specific code (e.g., `ERR_USER_SUSPENDED`) does not apply, such as a user attempting to access a resource outside of their assigned `CityFleet`.

| Error ID                                  | HTTP Status Code |
| :---------------------------------------- | :--------------- |
| `ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER` | 400 Bad Request  |
| `ERR_MISSING_QUOTE_ID`                    | 400 Bad Request  |
| `ERR_FORBIDDEN`                             | 403 Forbidden    |
| `ERR_USER_SUSPENDED`                        | 403 Forbidden    |
| `ERR_LUXURY_LOCK`                           | 403 Forbidden    |
| `ERR_REPUTATION_LOW`                        | 403 Forbidden    |
| `ERR_PROBATIONARY_RESTRICTION_HIGH_ALERT`   | 403 Forbidden    |
| `ERR_ZONE_NOT_FOUND`                        | 404 Not Found    |
| `ERR_ACTIVE_RENTAL_EXISTS`                  | 409 Conflict     |
| `ERR_QUOTE_EXISTS`                          | 409 Conflict     |
| `ERR_VEHICLE_NOT_RENTABLE`                  | 409 Conflict     |
| `ERR_VEHICLE_ZONE_MISMATCH`                 | 409 Conflict     |
| `ERR_ZONE_LOCKED_FOR_RENTAL`                | 409 Conflict     |
| `ERR_QUOTE_TAMPER`                          | 409 Conflict     |
| `ERR_RENTAL_NOT_CONFIRMED`                  | 409 Conflict     |
| `ERR_RENTAL_NOT_CANCELLABLE`                | 409 Conflict     |
| `ERR_RENTAL_NOT_ACTIVE`                     | 409 Conflict     |
| `ERR_ZONE_LOCKED_FOR_RETURN`                | 409 Conflict     |
| `ERR_HOLD_NOT_ACTIVE`                       | 409 Conflict     |
| `ERR_VEHICLE_NOT_IN_MAINTENANCE`            | 409 Conflict     |
| `ERR_VEHICLE_NOT_DIRTY`                     | 409 Conflict     |
| `ERR_RENTAL_NOT_IN_REVIEW`                  | 409 Conflict     |
| `ERR_HOLD_NOT_OVERRIDABLE`                  | 409 Conflict     |
| `ERR_AMBIGUOUS_OVERRIDE_HOLD`               | 409 Conflict     |
| `ERR_INVALID_ROLE`                          | 422 Unprocessable |
| `ERR_RESERVED_ZONE_CODE`                    | 422 Unprocessable |
| `ERR_AMBIGUOUS_ZONE_UPDATE`                 | 422 Unprocessable |
| `ERR_CANNOT_MODIFY_NON_RENTER`              | 422 Unprocessable |
| `ERR_INVALID_UPDATE_FIELDS`                 | 422 Unprocessable |
| `ERR_VEHICLE_CLASS_NOT_ALLOWED_IN_FLEET`    | 422 Unprocessable |
| `ERR_VEHICLE_MUST_HAVE_ZONE`                | 422 Unprocessable |
| `ERR_ODOMETER_ROLLBACK`                     | 422 Unprocessable |
| `ERR_VEHICLE_NOT_EV`                        | 422 Unprocessable |
| `ERR_INSUFFICIENT_MAINTENANCE_OVERRIDE_MILES`| 422 Unprocessable |
