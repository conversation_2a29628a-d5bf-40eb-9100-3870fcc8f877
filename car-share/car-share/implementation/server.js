import express from 'express';
import crypto from 'crypto';

const app = express();
app.use(express.json());

// In-memory storage
const storage = {
  fleets: new Map(),
  users: new Map(),
  parkingZones: new Map(),
  vehicles: new Map(),
  rentals: new Map(),
  quotes: new Map(),
  maintenanceHolds: new Map(),
  conditionReports: new Map(),
  
  // Ledgers
  fleetActionCounters: new Map(),
  zoneBalanceLedger: new Map(),
  vehicleMileageLedger: new Map(),
  renterReputationLedger: new Map()
};

// Helper functions
const generateId = (prefix) => {
  const chars = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
  let id = '';
  for (let i = 0; i < 26; i++) {
    id += chars[Math.floor(Math.random() * chars.length)];
  }
  return `${prefix}_${id}`;
};

const generateRequestId = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let id = '';
  for (let i = 0; i < 20; i++) {
    id += chars[Math.floor(Math.random() * chars.length)];
  }
  return `req_${id}`;
};

const generateTimestamp = () => new Date().toISOString();

const createResponse = (data, responseType = 'object') => ({
  meta: {
    api_request_id: generateRequestId(),
    api_request_timestamp: generateTimestamp()
  },
  response_type: responseType,
  data: data
});

const createErrorResponse = (errorId, message) => ({
  meta: {
    api_request_id: generateRequestId(),
    api_request_timestamp: generateTimestamp()
  },
  response_type: 'error',
  data: {
    error_id: errorId,
    message: message
  }
});

const hashPrice = (price) => {
  return crypto.createHash('sha256').update(price.toString()).digest('hex');
};

// Middleware
const authenticateUser = (req, res, next) => {
  const userId = req.headers['x-user-id'];
  const userRole = req.headers['x-user-role'];
  
  if (!userId || !userRole || !userId.match(/^user_[0-9A-HJKMNP-TV-Z]{26}$/) || 
      !['RENTER', 'FLEET_OPERATOR', 'MECHANIC'].includes(userRole)) {
    return res.status(400).json(createErrorResponse(
      'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
      'Valid X-User-ID and X-User-Role headers are required'
    ));
  }
  
  req.userId = userId;
  req.userRole = userRole;
  next();
};

const incrementFleetActionCounter = (fleetId) => {
  const current = storage.fleetActionCounters.get(fleetId) || 0;
  storage.fleetActionCounters.set(fleetId, current + 1);
};

const getFleetActionCounter = (fleetId) => {
  return storage.fleetActionCounters.get(fleetId) || 0;
};

const updateZoneBalance = (zoneId, delta) => {
  const current = storage.zoneBalanceLedger.get(zoneId) || { surplus: 0 };
  current.surplus += delta;
  storage.zoneBalanceLedger.set(zoneId, current);
  
  const zone = storage.parkingZones.get(zoneId);
  if (zone && !zone.manual_lock) {
    if (current.surplus <= -10) {
      zone.lock_state = 'HARD_LOCK';
    } else if (current.surplus <= -5) {
      zone.lock_state = 'SOFT_LOCK';
    } else if (zone.lock_state !== 'HARD_LOCK') {
      zone.lock_state = 'OPEN';
    }
  }
  
  updateFleetPhaseState(zone.fleet_id);
};

const updateFleetPhaseState = (fleetId) => {
  const fleet = storage.fleets.get(fleetId);
  if (!fleet) return;
  
  let hardLockCount = 0;
  for (const [_, zone] of storage.parkingZones) {
    if (zone.fleet_id === fleetId && zone.lock_state === 'HARD_LOCK') {
      hardLockCount++;
    }
  }
  
  fleet.phase_state = hardLockCount >= 3 ? 'HIGH_ALERT' : 'NORMAL';
};

const updateVehicleMileage = (vehicleId, miles) => {
  const current = storage.vehicleMileageLedger.get(vehicleId) || { 
    miles_since_service: 0, 
    override_miles_credit: 0 
  };
  current.miles_since_service += miles;
  storage.vehicleMileageLedger.set(vehicleId, current);
};

const updateRenterReputation = (renterId, delta) => {
  const ledger = storage.renterReputationLedger.get(renterId);
  if (ledger) {
    ledger.reputation_score = Math.max(0, Math.min(300, ledger.reputation_score + delta));
    
    const user = storage.users.get(renterId);
    if (user) {
      user.luxury_unlock = ledger.reputation_score >= 150;
      
      if (ledger.reputation_score < 75) {
        user.account_status = 'PROBATIONARY';
      } else if (ledger.reputation_score >= 85 && user.account_status === 'PROBATIONARY') {
        user.account_status = 'GOOD_STANDING';
      }
    }
  }
};

const calculateMilesUntilService = (vehicle) => {
  const mileageLedger = storage.vehicleMileageLedger.get(vehicle.id) || { miles_since_service: 0 };
  const milesSinceService = vehicle.odometer_mi - vehicle.last_service_odometer_mi;
  return vehicle.service_interval_mi - milesSinceService;
};

const isVehicleRentable = (vehicle, renterId) => {
  if (vehicle.state === 'AVAILABLE') {
    if (vehicle.class === 'EV' && vehicle.battery_pct < 20) return false;
    if (calculateMilesUntilService(vehicle) <= 0) return false;
    return true;
  }
  
  if (vehicle.state === 'NEEDS_MAINTENANCE') {
    const renter = storage.users.get(renterId);
    const repLedger = storage.renterReputationLedger.get(renterId);
    if (!repLedger || repLedger.reputation_score <= 120) return false;
    
    const activeHolds = Array.from(storage.maintenanceHolds.values())
      .filter(h => h.vehicle_id === vehicle.id && h.active);
    
    return activeHolds.length === 1 && activeHolds[0].override_allowed;
  }
  
  return false;
};

// Endpoints

// POST /city-fleets
app.post('/city-fleets', authenticateUser, (req, res) => {
  if (req.userRole !== 'FLEET_OPERATOR') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only fleet operators can create fleets'));
  }
  
  const { name, city_code, allowed_vehicle_classes } = req.body;
  
  if (!name || name.length < 3 || name.length > 50) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_NAME', 'Name must be 3-50 characters'));
  }
  
  if (!city_code || !city_code.match(/^[A-Z]{2,3}$/)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_CITY_CODE', 'City code must be 2-3 uppercase letters'));
  }
  
  if (!Array.isArray(allowed_vehicle_classes) || allowed_vehicle_classes.length === 0) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_VEHICLE_CLASSES', 'Allowed vehicle classes must be a non-empty array'));
  }
  
  const fleet = {
    id: generateId('fleet'),
    name,
    city_code,
    allowed_vehicle_classes,
    phase_state: 'NORMAL',
    created_at: generateTimestamp()
  };
  
  storage.fleets.set(fleet.id, fleet);
  storage.fleetActionCounters.set(fleet.id, 0);
  
  incrementFleetActionCounter(fleet.id);
  res.status(201).json(createResponse(fleet));
});

// GET /city-fleets/{fleet_id}
app.get('/city-fleets/:fleet_id', authenticateUser, (req, res) => {
  const fleet = storage.fleets.get(req.params.fleet_id);
  if (!fleet) {
    return res.status(404).json(createErrorResponse('ERR_FLEET_NOT_FOUND', 'Fleet not found'));
  }
  
  res.json(createResponse(fleet));
});

// POST /city-fleets/{fleet_id}/users
app.post('/city-fleets/:fleet_id/users', authenticateUser, (req, res) => {
  const fleet = storage.fleets.get(req.params.fleet_id);
  if (!fleet) {
    return res.status(404).json(createErrorResponse('ERR_FLEET_NOT_FOUND', 'Fleet not found'));
  }
  
  const { role, name, email, phone } = req.body;
  
  if (!role || !['RENTER', 'FLEET_OPERATOR', 'MECHANIC'].includes(role)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_ROLE', 'Invalid role'));
  }
  
  if (!name || name.length < 2 || name.length > 40) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_NAME', 'Name must be 2-40 characters'));
  }
  
  if (!email || !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_EMAIL', 'Invalid email format'));
  }
  
  if (phone && !phone.match(/^\+[1-9]\d{1,14}$/)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_PHONE', 'Phone must be in E.164 format'));
  }
  
  const user = {
    id: generateId('user'),
    fleet_id: req.params.fleet_id,
    role,
    name,
    email,
    phone: phone || null,
    created_at: generateTimestamp()
  };
  
  if (role === 'RENTER') {
    user.active_rental_id = null;
    user.suspended = false;
    user.account_status = 'GOOD_STANDING';
    user.luxury_unlock = false;
    user.late_return_history = [];
    
    storage.renterReputationLedger.set(user.id, {
      reputation_score: 100,
      late_counter: 0
    });
  }
  
  storage.users.set(user.id, user);
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.status(201).json(createResponse(user));
});

// POST /city-fleets/{fleet_id}/parking-zones
app.post('/city-fleets/:fleet_id/parking-zones', authenticateUser, (req, res) => {
  if (req.userRole !== 'FLEET_OPERATOR') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only fleet operators can create zones'));
  }
  
  const fleet = storage.fleets.get(req.params.fleet_id);
  if (!fleet) {
    return res.status(404).json(createErrorResponse('ERR_FLEET_NOT_FOUND', 'Fleet not found'));
  }
  
  const { display_name, code, geojson, target_vehicles, incentivized_classes, discouraged_classes } = req.body;
  
  if (!display_name || display_name.length < 3 || display_name.length > 32) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_DISPLAY_NAME', 'Display name must be 3-32 characters'));
  }
  
  if (!code || !code.match(/^[A-Z]{3}-\d{2}$/)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_ZONE_CODE', 'Zone code must match pattern ^[A-Z]{3}-\\d{2}$'));
  }
  
  if (code.startsWith('EA-')) {
    return res.status(422).json(createErrorResponse('ERR_RESERVED_ZONE_CODE', 'Zone codes starting with EA- are reserved'));
  }
  
  if (!geojson || typeof geojson !== 'string') {
    return res.status(422).json(createErrorResponse('ERR_INVALID_GEOJSON', 'GeoJSON must be a string'));
  }
  
  if (typeof target_vehicles !== 'number' || target_vehicles < 0 || target_vehicles > 500) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_TARGET_VEHICLES', 'Target vehicles must be 0-500'));
  }
  
  const zone = {
    id: generateId('zone'),
    fleet_id: req.params.fleet_id,
    display_name,
    code,
    geojson,
    target_vehicles,
    lock_state: 'OPEN',
    manual_lock: false,
    incentivized_classes: incentivized_classes || [],
    discouraged_classes: discouraged_classes || [],
    created_at: generateTimestamp()
  };
  
  storage.parkingZones.set(zone.id, zone);
  storage.zoneBalanceLedger.set(zone.id, { surplus: 0 });
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.status(201).json(createResponse(zone));
});

// POST /city-fleets/{fleet_id}/vehicles
app.post('/city-fleets/:fleet_id/vehicles', authenticateUser, (req, res) => {
  if (req.userRole !== 'MECHANIC') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only mechanics can register vehicles'));
  }
  
  const fleet = storage.fleets.get(req.params.fleet_id);
  if (!fleet) {
    return res.status(404).json(createErrorResponse('ERR_FLEET_NOT_FOUND', 'Fleet not found'));
  }
  
  const { vin, license_plate, class: vehicleClass, odometer_mi, service_interval_mi, 
          last_service_odometer_mi, current_zone_id, battery_pct } = req.body;
  
  if (!vin || vin.length !== 17) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_VIN', 'VIN must be 17 characters'));
  }
  
  if (!license_plate || !license_plate.match(/^[A-Z0-9]{2,8}$/)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_LICENSE_PLATE', 'License plate must be 2-8 alphanumeric characters'));
  }
  
  if (!vehicleClass || !fleet.allowed_vehicle_classes.includes(vehicleClass)) {
    return res.status(422).json(createErrorResponse('ERR_VEHICLE_CLASS_NOT_ALLOWED_IN_FLEET', 
      'Vehicle class not allowed in this fleet'));
  }
  
  if (typeof odometer_mi !== 'number' || odometer_mi < 0) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_ODOMETER', 'Odometer must be non-negative'));
  }
  
  if (typeof service_interval_mi !== 'number' || service_interval_mi < 1000 || service_interval_mi > 20000) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_SERVICE_INTERVAL', 'Service interval must be 1000-20000'));
  }
  
  if (typeof last_service_odometer_mi !== 'number' || last_service_odometer_mi !== odometer_mi) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_LAST_SERVICE_ODOMETER', 
      'Last service odometer must equal initial odometer for new vehicles'));
  }
  
  if (!current_zone_id) {
    return res.status(422).json(createErrorResponse('ERR_VEHICLE_MUST_HAVE_ZONE', 
      'Vehicle must be assigned to a zone'));
  }
  
  const zone = storage.parkingZones.get(current_zone_id);
  if (!zone || zone.fleet_id !== req.params.fleet_id) {
    return res.status(422).json(createErrorResponse('ERR_ZONE_NOT_FOUND', 'Zone not found in fleet'));
  }
  
  if (vehicleClass === 'EV' && (typeof battery_pct !== 'number' || battery_pct < 0 || battery_pct > 100)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_BATTERY_PCT', 'EV vehicles require battery_pct 0-100'));
  }
  
  const vehicle = {
    id: generateId('vehicle'),
    fleet_id: req.params.fleet_id,
    vin,
    license_plate,
    class: vehicleClass,
    odometer_mi,
    service_interval_mi,
    last_service_odometer_mi,
    current_zone_id,
    state: 'AVAILABLE',
    flags: [],
    miles_until_service: service_interval_mi,
    created_at: generateTimestamp()
  };
  
  if (vehicleClass === 'EV') {
    vehicle.battery_pct = battery_pct;
  }
  
  storage.vehicles.set(vehicle.id, vehicle);
  storage.vehicleMileageLedger.set(vehicle.id, { 
    miles_since_service: 0,
    override_miles_credit: 0
  });
  
  updateZoneBalance(current_zone_id, 1);
  incrementFleetActionCounter(req.params.fleet_id);
  res.status(201).json(createResponse(vehicle));
});

// POST /city-fleets/{fleet_id}/maintenance-holds
app.post('/city-fleets/:fleet_id/maintenance-holds', authenticateUser, (req, res) => {
  if (req.userRole !== 'MECHANIC') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only mechanics can create maintenance holds'));
  }
  
  const fleet = storage.fleets.get(req.params.fleet_id);
  if (!fleet) {
    return res.status(404).json(createErrorResponse('ERR_FLEET_NOT_FOUND', 'Fleet not found'));
  }
  
  const { vehicle_id, reason_code, override_allowed, allowed_miles_remaining } = req.body;
  
  const vehicle = storage.vehicles.get(vehicle_id);
  if (!vehicle || vehicle.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_VEHICLE_NOT_FOUND', 'Vehicle not found in fleet'));
  }
  
  if (!['OIL_CHANGE', 'BRAKE_INSPECT', 'TIRE_ROTATION', 'OTHER'].includes(reason_code)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_REASON_CODE', 'Invalid reason code'));
  }
  
  if (override_allowed && (typeof allowed_miles_remaining !== 'number' || 
      allowed_miles_remaining < 0 || allowed_miles_remaining > 200)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_ALLOWED_MILES', 
      'Allowed miles must be 0-200 when override is allowed'));
  }
  
  const hold = {
    id: generateId('hold'),
    fleet_id: req.params.fleet_id,
    vehicle_id,
    reason_code,
    active: true,
    override_allowed: override_allowed || false,
    allowed_miles_remaining: override_allowed ? allowed_miles_remaining : 0,
    created_at: generateTimestamp()
  };
  
  storage.maintenanceHolds.set(hold.id, hold);
  
  if (!vehicle.flags.includes('MAINT_HOLD')) {
    vehicle.flags.push('MAINT_HOLD');
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.status(201).json(createResponse(hold));
});

// POST /city-fleets/{fleet_id}/condition-reports
app.post('/city-fleets/:fleet_id/condition-reports', authenticateUser, (req, res) => {
  const fleet = storage.fleets.get(req.params.fleet_id);
  if (!fleet) {
    return res.status(404).json(createErrorResponse('ERR_FLEET_NOT_FOUND', 'Fleet not found'));
  }
  
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  const { vehicle_id, report_type, damage_grade, cleanliness_grade } = req.body;
  
  const vehicle = storage.vehicles.get(vehicle_id);
  if (!vehicle || vehicle.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_VEHICLE_NOT_FOUND', 'Vehicle not found in fleet'));
  }
  
  if (!['RETURN', 'SPOT', 'MAINT_CHECK'].includes(report_type)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_REPORT_TYPE', 'Invalid report type'));
  }
  
  if (!['NONE', 'MINOR', 'MAJOR'].includes(damage_grade)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_DAMAGE_GRADE', 'Invalid damage grade'));
  }
  
  if (!['EXCELLENT', 'GOOD', 'FAIR', 'POOR'].includes(cleanliness_grade)) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_CLEANLINESS_GRADE', 'Invalid cleanliness grade'));
  }
  
  const report = {
    id: generateId('report'),
    fleet_id: req.params.fleet_id,
    vehicle_id,
    reporter_id: req.userId,
    report_type,
    damage_grade,
    cleanliness_grade,
    created_at: generateTimestamp()
  };
  
  storage.conditionReports.set(report.id, report);
  
  if (damage_grade === 'MAJOR' && vehicle.state !== 'RETIRED') {
    vehicle.state = 'NEEDS_MAINTENANCE';
    
    if (vehicle.state === 'RESERVED') {
      const rental = Array.from(storage.rentals.values())
        .find(r => r.vehicle_id === vehicle_id && r.state === 'CONFIRMED');
      if (rental) {
        rental.state = 'CANCELLED';
        const renter = storage.users.get(rental.renter_id);
        if (renter) {
          renter.active_rental_id = null;
        }
      }
    }
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.status(201).json(createResponse(report));
});

// GET /city-fleets/{fleet_id}/summary
app.get('/city-fleets/:fleet_id/summary', authenticateUser, (req, res) => {
  const fleet = storage.fleets.get(req.params.fleet_id);
  if (!fleet) {
    return res.status(404).json(createErrorResponse('ERR_FLEET_NOT_FOUND', 'Fleet not found'));
  }
  
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  let summary = {};
  
  if (req.userRole === 'FLEET_OPERATOR') {
    const vehicles = Array.from(storage.vehicles.values()).filter(v => v.fleet_id === req.params.fleet_id);
    const zones = Array.from(storage.parkingZones.values()).filter(z => z.fleet_id === req.params.fleet_id);
    
    summary = {
      fleet_id: req.params.fleet_id,
      phase_state: fleet.phase_state,
      total_vehicles: vehicles.length,
      vehicles_by_state: {
        AVAILABLE: vehicles.filter(v => v.state === 'AVAILABLE').length,
        IN_USE: vehicles.filter(v => v.state === 'IN_USE').length,
        RESERVED: vehicles.filter(v => v.state === 'RESERVED').length,
        NEEDS_MAINTENANCE: vehicles.filter(v => v.state === 'NEEDS_MAINTENANCE').length,
        NEEDS_CLEANING: vehicles.filter(v => v.state === 'NEEDS_CLEANING').length,
        RETIRED: vehicles.filter(v => v.state === 'RETIRED').length
      },
      zones_summary: {
        total: zones.length,
        locked: zones.filter(z => z.lock_state !== 'OPEN').length,
        hard_locked: zones.filter(z => z.lock_state === 'HARD_LOCK').length
      }
    };
  } else if (req.userRole === 'RENTER') {
    const repLedger = storage.renterReputationLedger.get(req.userId);
    summary = {
      user_id: req.userId,
      reputation_score: repLedger ? repLedger.reputation_score : 100,
      account_status: user.account_status,
      luxury_unlock: user.luxury_unlock,
      active_rental_id: user.active_rental_id,
      suspended: user.suspended
    };
  } else if (req.userRole === 'MECHANIC') {
    const activeHolds = Array.from(storage.maintenanceHolds.values())
      .filter(h => h.fleet_id === req.params.fleet_id && h.active);
    
    summary = {
      fleet_id: req.params.fleet_id,
      active_maintenance_holds: activeHolds.length,
      vehicles_needing_service: Array.from(storage.vehicles.values())
        .filter(v => v.fleet_id === req.params.fleet_id && v.state === 'NEEDS_MAINTENANCE').length
    };
  }
  
  res.json(createResponse(summary));
});

// PATCH /city-fleets/{fleet_id}/users/{user_id}
app.patch('/city-fleets/:fleet_id/users/:user_id', authenticateUser, (req, res) => {
  if (req.userRole !== 'FLEET_OPERATOR') {
    return res.status(422).json(createErrorResponse('ERR_INVALID_ROLE', 'Only fleet operators can modify users'));
  }
  
  const user = storage.users.get(req.params.user_id);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_USER_NOT_FOUND', 'User not found in fleet'));
  }
  
  if (user.role !== 'RENTER') {
    return res.status(422).json(createErrorResponse('ERR_CANNOT_MODIFY_NON_RENTER', 'Can only modify renters'));
  }
  
  const allowedFields = ['suspended'];
  const invalidFields = Object.keys(req.body).filter(f => !allowedFields.includes(f));
  if (invalidFields.length > 0) {
    return res.status(422).json(createErrorResponse('ERR_INVALID_UPDATE_FIELDS', 'Invalid update fields'));
  }
  
  if ('suspended' in req.body) {
    user.suspended = req.body.suspended;
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse(user));
});

// PATCH /city-fleets/{fleet_id}/parking-zones/{zone_id}
app.patch('/city-fleets/:fleet_id/parking-zones/:zone_id', authenticateUser, (req, res) => {
  if (req.userRole !== 'FLEET_OPERATOR') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only fleet operators can update zones'));
  }
  
  const zone = storage.parkingZones.get(req.params.zone_id);
  if (!zone || zone.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_ZONE_NOT_FOUND', 'Zone not found in fleet'));
  }
  
  const { target_vehicles, lock_state, manual_lock } = req.body;
  
  const hasTargetVehicles = 'target_vehicles' in req.body;
  const hasLockState = 'lock_state' in req.body;
  const hasManualLock = 'manual_lock' in req.body;
  
  if (hasTargetVehicles && (hasLockState || hasManualLock)) {
    return res.status(422).json(createErrorResponse('ERR_AMBIGUOUS_ZONE_UPDATE', 
      'Cannot update target_vehicles with lock configuration'));
  }
  
  if (hasTargetVehicles) {
    if (typeof target_vehicles !== 'number' || target_vehicles < 0 || target_vehicles > 500) {
      return res.status(422).json(createErrorResponse('ERR_INVALID_TARGET_VEHICLES', 'Target vehicles must be 0-500'));
    }
    zone.target_vehicles = target_vehicles;
  }
  
  if (hasLockState) {
    if (!['OPEN', 'SOFT_LOCK', 'HARD_LOCK'].includes(lock_state)) {
      return res.status(422).json(createErrorResponse('ERR_INVALID_LOCK_STATE', 'Invalid lock state'));
    }
    zone.lock_state = lock_state;
    zone.manual_lock = true;
  }
  
  if (hasManualLock) {
    zone.manual_lock = manual_lock;
  }
  
  updateFleetPhaseState(req.params.fleet_id);
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse(zone));
});

// POST /city-fleets/{fleet_id}/vehicles/{vehicle_id}/retire
app.post('/city-fleets/:fleet_id/vehicles/:vehicle_id/retire', authenticateUser, (req, res) => {
  if (req.userRole !== 'FLEET_OPERATOR') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only fleet operators can retire vehicles'));
  }
  
  const vehicle = storage.vehicles.get(req.params.vehicle_id);
  if (!vehicle || vehicle.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_VEHICLE_NOT_FOUND', 'Vehicle not found in fleet'));
  }
  
  vehicle.state = 'RETIRED';
  
  if (vehicle.current_zone_id) {
    updateZoneBalance(vehicle.current_zone_id, -1);
    vehicle.current_zone_id = null;
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse({ message: 'Vehicle retired successfully' }));
});

// PATCH /city-fleets/{fleet_id}/vehicles/{vehicle_id}/service-status
app.patch('/city-fleets/:fleet_id/vehicles/:vehicle_id/service-status', authenticateUser, (req, res) => {
  if (req.userRole !== 'FLEET_OPERATOR') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only fleet operators can update service status'));
  }
  
  const vehicle = storage.vehicles.get(req.params.vehicle_id);
  if (!vehicle || vehicle.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_VEHICLE_NOT_FOUND', 'Vehicle not found in fleet'));
  }
  
  if (vehicle.state !== 'NEEDS_CLEANING') {
    return res.status(409).json(createErrorResponse('ERR_VEHICLE_NOT_DIRTY', 'Vehicle is not in needs cleaning state'));
  }
  
  vehicle.flags = vehicle.flags.filter(f => f !== 'CLEAN_REQ');
  
  if (calculateMilesUntilService(vehicle) <= 0 || vehicle.flags.includes('MAINT_HOLD')) {
    vehicle.state = 'NEEDS_MAINTENANCE';
  } else {
    vehicle.state = 'AVAILABLE';
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse(vehicle));
});

// POST /city-fleets/{fleet_id}/vehicles/{vehicle_id}/service
app.post('/city-fleets/:fleet_id/vehicles/:vehicle_id/service', authenticateUser, (req, res) => {
  if (req.userRole !== 'MECHANIC') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only mechanics can service vehicles'));
  }
  
  const vehicle = storage.vehicles.get(req.params.vehicle_id);
  if (!vehicle || vehicle.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_VEHICLE_NOT_FOUND', 'Vehicle not found in fleet'));
  }
  
  if (vehicle.state !== 'NEEDS_MAINTENANCE') {
    return res.status(409).json(createErrorResponse('ERR_VEHICLE_NOT_IN_MAINTENANCE', 
      'Vehicle is not in maintenance state'));
  }
  
  vehicle.last_service_odometer_mi = vehicle.odometer_mi;
  const mileageLedger = storage.vehicleMileageLedger.get(req.params.vehicle_id);
  if (mileageLedger) {
    mileageLedger.miles_since_service = 0;
  }
  
  if (vehicle.flags.includes('CLEAN_REQ')) {
    vehicle.state = 'NEEDS_CLEANING';
  } else {
    vehicle.state = 'AVAILABLE';
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse({ message: 'Vehicle serviced successfully' }));
});

// POST /city-fleets/{fleet_id}/vehicles/{vehicle_id}/charge
app.post('/city-fleets/:fleet_id/vehicles/:vehicle_id/charge', authenticateUser, (req, res) => {
  if (req.userRole !== 'MECHANIC') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only mechanics can charge vehicles'));
  }
  
  const vehicle = storage.vehicles.get(req.params.vehicle_id);
  if (!vehicle || vehicle.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_VEHICLE_NOT_FOUND', 'Vehicle not found in fleet'));
  }
  
  if (vehicle.class !== 'EV') {
    return res.status(422).json(createErrorResponse('ERR_VEHICLE_NOT_EV', 'Vehicle is not an EV'));
  }
  
  vehicle.battery_pct = 100;
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse(vehicle));
});

// PATCH /city-fleets/{fleet_id}/maintenance-holds/{hold_id}/release
app.patch('/city-fleets/:fleet_id/maintenance-holds/:hold_id/release', authenticateUser, (req, res) => {
  if (req.userRole !== 'MECHANIC') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only mechanics can release holds'));
  }
  
  const hold = storage.maintenanceHolds.get(req.params.hold_id);
  if (!hold || hold.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_HOLD_NOT_FOUND', 'Maintenance hold not found'));
  }
  
  if (!hold.active) {
    return res.status(409).json(createErrorResponse('ERR_HOLD_NOT_ACTIVE', 'Hold is not active'));
  }
  
  hold.active = false;
  
  const vehicle = storage.vehicles.get(hold.vehicle_id);
  if (vehicle) {
    vehicle.flags = vehicle.flags.filter(f => f !== 'MAINT_HOLD');
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse({ message: 'Hold released successfully' }));
});

// POST /city-fleets/{fleet_id}/rentals/quote
app.post('/city-fleets/:fleet_id/rentals/quote', authenticateUser, (req, res) => {
  if (req.userRole !== 'RENTER') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only renters can request quotes'));
  }
  
  const fleet = storage.fleets.get(req.params.fleet_id);
  if (!fleet) {
    return res.status(404).json(createErrorResponse('ERR_FLEET_NOT_FOUND', 'Fleet not found'));
  }
  
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  if (user.suspended) {
    return res.status(403).json(createErrorResponse('ERR_USER_SUSPENDED', 'User is suspended'));
  }
  
  if (user.account_status === 'PROBATIONARY' && fleet.phase_state === 'HIGH_ALERT') {
    return res.status(403).json(createErrorResponse('ERR_PROBATIONARY_RESTRICTION_HIGH_ALERT', 
      'Probationary users cannot rent during high alert'));
  }
  
  if (user.active_rental_id) {
    return res.status(409).json(createErrorResponse('ERR_ACTIVE_RENTAL_EXISTS', 'User already has active rental'));
  }
  
  const existingQuote = Array.from(storage.quotes.values())
    .find(q => q.renter_id === req.userId && !q.used);
  if (existingQuote) {
    return res.status(409).json(createErrorResponse('ERR_QUOTE_EXISTS', 'User already has active quote'));
  }
  
  const { vehicle_id, start_zone_id, end_zone_id, distance_estimate_mi } = req.body;
  
  const vehicle = storage.vehicles.get(vehicle_id);
  if (!vehicle || vehicle.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_VEHICLE_NOT_FOUND', 'Vehicle not found in fleet'));
  }
  
  const repLedger = storage.renterReputationLedger.get(req.userId);
  if (vehicle.class === 'LUXURY' && (!repLedger || repLedger.reputation_score < 150)) {
    return res.status(403).json(createErrorResponse('ERR_LUXURY_LOCK', 'Insufficient reputation for luxury vehicles'));
  }
  
  if (!isVehicleRentable(vehicle, req.userId)) {
    return res.status(409).json(createErrorResponse('ERR_VEHICLE_NOT_RENTABLE', 'Vehicle is not rentable'));
  }
  
  if (vehicle.state === 'NEEDS_MAINTENANCE') {
    const activeHolds = Array.from(storage.maintenanceHolds.values())
      .filter(h => h.vehicle_id === vehicle_id && h.active);
    
    if (activeHolds.length !== 1) {
      return res.status(409).json(createErrorResponse('ERR_AMBIGUOUS_OVERRIDE_HOLD', 
        'Multiple active holds prevent override'));
    }
    
    const hold = activeHolds[0];
    if (!hold.override_allowed) {
      return res.status(409).json(createErrorResponse('ERR_HOLD_NOT_OVERRIDABLE', 'Hold does not allow override'));
    }
    
    if (!repLedger || repLedger.reputation_score <= 120) {
      return res.status(403).json(createErrorResponse('ERR_REPUTATION_LOW', 
        'Insufficient reputation for maintenance override'));
    }
    
    if (distance_estimate_mi > hold.allowed_miles_remaining) {
      return res.status(422).json(createErrorResponse('ERR_INSUFFICIENT_MAINTENANCE_OVERRIDE_MILES', 
        'Distance exceeds allowed override miles'));
    }
  }
  
  if (vehicle.current_zone_id !== start_zone_id) {
    return res.status(409).json(createErrorResponse('ERR_VEHICLE_ZONE_MISMATCH', 
      'Vehicle not in specified start zone'));
  }
  
  const startZone = storage.parkingZones.get(start_zone_id);
  if (!startZone || startZone.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_ZONE_NOT_FOUND', 'Start zone not found'));
  }
  
  if (startZone.lock_state !== 'OPEN') {
    return res.status(409).json(createErrorResponse('ERR_ZONE_LOCKED_FOR_RENTAL', 'Start zone is locked'));
  }
  
  const endZone = storage.parkingZones.get(end_zone_id);
  if (!endZone || endZone.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_ZONE_NOT_FOUND', 'End zone not found'));
  }
  
  // Calculate price
  let basePrice = 1000; // Base price in cents
  if (vehicle.class === 'LUXURY') basePrice *= 2;
  if (vehicle.class === 'EV') basePrice *= 1.3;
  
  const startBalance = storage.zoneBalanceLedger.get(start_zone_id)?.surplus || 0;
  const endBalance = storage.zoneBalanceLedger.get(end_zone_id)?.surplus || 0;
  
  if (startBalance < -3) basePrice *= 0.9;
  if (endBalance < -3) basePrice *= 1.1;
  
  let price = Math.floor(basePrice);
  
  if (fleet.phase_state === 'HIGH_ALERT') {
    price = Math.floor(price * 1.15);
  }
  
  if (startZone.discouraged_classes.includes(vehicle.class)) {
    price = Math.floor(price * 1.05);
  }
  
  const quotedActionDuration = user.account_status === 'PROBATIONARY' 
    ? 10 + Math.floor(distance_estimate_mi / 5)
    : 20 + Math.floor(distance_estimate_mi / 4);
  
  const quote = {
    quote_id: generateId('quote'),
    fleet_id: req.params.fleet_id,
    renter_id: req.userId,
    vehicle_id,
    start_zone_id,
    end_zone_id,
    distance_estimate_mi,
    quoted_price_cents: price,
    price_hash: hashPrice(price),
    quoted_action_duration: quotedActionDuration,
    used: false,
    created_at: generateTimestamp()
  };
  
  storage.quotes.set(quote.quote_id, quote);
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.status(201).json(createResponse(quote));
});

// DELETE /city-fleets/{fleet_id}/rentals/quote/{quote_id}
app.delete('/city-fleets/:fleet_id/rentals/quote/:quote_id', authenticateUser, (req, res) => {
  if (req.userRole !== 'RENTER') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only renters can cancel quotes'));
  }
  
  const quote = storage.quotes.get(req.params.quote_id);
  if (!quote || quote.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_QUOTE_NOT_FOUND', 'Quote not found'));
  }
  
  if (quote.renter_id !== req.userId) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Cannot cancel another user\'s quote'));
  }
  
  if (quote.used) {
    return res.status(409).json(createErrorResponse('ERR_QUOTE_ALREADY_USED', 'Quote already used'));
  }
  
  storage.quotes.delete(req.params.quote_id);
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse({ message: 'Quote cancelled successfully' }));
});

// POST /city-fleets/{fleet_id}/rentals
app.post('/city-fleets/:fleet_id/rentals', authenticateUser, (req, res) => {
  if (req.userRole !== 'RENTER') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only renters can confirm rentals'));
  }
  
  const { quote_id, quoted_price_cents } = req.body;
  
  if (!quote_id) {
    return res.status(400).json(createErrorResponse('ERR_MISSING_QUOTE_ID', 'Quote ID is required'));
  }
  
  const quote = storage.quotes.get(quote_id);
  if (!quote || quote.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_QUOTE_NOT_FOUND', 'Quote not found'));
  }
  
  if (quote.renter_id !== req.userId) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Cannot use another user\'s quote'));
  }
  
  if (quote.used) {
    return res.status(409).json(createErrorResponse('ERR_QUOTE_ALREADY_USED', 'Quote already used'));
  }
  
  if (hashPrice(quoted_price_cents) !== quote.price_hash) {
    return res.status(409).json(createErrorResponse('ERR_QUOTE_TAMPER', 'Quote price mismatch'));
  }
  
  const user = storage.users.get(req.userId);
  if (user.suspended) {
    return res.status(403).json(createErrorResponse('ERR_USER_SUSPENDED', 'User is suspended'));
  }
  
  if (user.active_rental_id) {
    return res.status(409).json(createErrorResponse('ERR_ACTIVE_RENTAL_EXISTS', 'User already has active rental'));
  }
  
  const vehicle = storage.vehicles.get(quote.vehicle_id);
  if (!isVehicleRentable(vehicle, req.userId)) {
    return res.status(409).json(createErrorResponse('ERR_VEHICLE_NOT_RENTABLE', 'Vehicle is not rentable'));
  }
  
  const rental = {
    id: generateId('rental'),
    fleet_id: req.params.fleet_id,
    renter_id: req.userId,
    vehicle_id: quote.vehicle_id,
    start_zone_id: quote.start_zone_id,
    end_zone_id: quote.end_zone_id,
    distance_estimate_mi: quote.distance_estimate_mi,
    quoted_price_cents: quote.quoted_price_cents,
    quoted_action_duration: quote.quoted_action_duration,
    state: 'CONFIRMED',
    created_at: generateTimestamp()
  };
  
  storage.rentals.set(rental.id, rental);
  quote.used = true;
  user.active_rental_id = rental.id;
  vehicle.state = 'RESERVED';
  
  if (vehicle.state === 'NEEDS_MAINTENANCE') {
    vehicle.flags.push('MAINT_OVERRIDE_GRANTED');
    const mileageLedger = storage.vehicleMileageLedger.get(vehicle.id);
    if (mileageLedger) {
      mileageLedger.override_miles_credit = quote.distance_estimate_mi;
    }
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.status(201).json(createResponse(rental));
});

// DELETE /city-fleets/{fleet_id}/rentals/{rental_id}
app.delete('/city-fleets/:fleet_id/rentals/:rental_id', authenticateUser, (req, res) => {
  if (req.userRole !== 'RENTER') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only renters can cancel rentals'));
  }
  
  const rental = storage.rentals.get(req.params.rental_id);
  if (!rental || rental.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_RENTAL_NOT_FOUND', 'Rental not found'));
  }
  
  if (rental.renter_id !== req.userId) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Cannot cancel another user\'s rental'));
  }
  
  if (rental.state !== 'CONFIRMED') {
    return res.status(409).json(createErrorResponse('ERR_RENTAL_NOT_CANCELLABLE', 'Rental cannot be cancelled'));
  }
  
  rental.state = 'CANCELLED';
  
  const user = storage.users.get(req.userId);
  user.active_rental_id = null;
  
  const vehicle = storage.vehicles.get(rental.vehicle_id);
  if (vehicle.flags.includes('MAINT_OVERRIDE_GRANTED')) {
    vehicle.flags = vehicle.flags.filter(f => f !== 'MAINT_OVERRIDE_GRANTED');
    vehicle.state = 'NEEDS_MAINTENANCE';
  } else {
    vehicle.state = 'AVAILABLE';
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse({ message: 'Rental cancelled successfully' }));
});

// PATCH /city-fleets/{fleet_id}/rentals/{rental_id}
app.patch('/city-fleets/:fleet_id/rentals/:rental_id', authenticateUser, (req, res) => {
  if (req.userRole !== 'RENTER') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only renters can activate rentals'));
  }
  
  const rental = storage.rentals.get(req.params.rental_id);
  if (!rental || rental.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_RENTAL_NOT_FOUND', 'Rental not found'));
  }
  
  if (rental.renter_id !== req.userId) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Cannot activate another user\'s rental'));
  }
  
  if (rental.state !== 'CONFIRMED') {
    return res.status(409).json(createErrorResponse('ERR_RENTAL_NOT_CONFIRMED', 'Rental is not confirmed'));
  }
  
  const vehicle = storage.vehicles.get(rental.vehicle_id);
  rental.start_odometer_mi = vehicle.odometer_mi;
  rental.start_action_count = getFleetActionCounter(req.params.fleet_id);
  rental.state = 'ACTIVE';
  
  vehicle.state = 'IN_USE';
  vehicle.current_zone_id = null;
  
  updateZoneBalance(rental.start_zone_id, -1);
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse(rental));
});

// PATCH /city-fleets/{fleet_id}/rentals/{rental_id}/return
app.patch('/city-fleets/:fleet_id/rentals/:rental_id/return', authenticateUser, (req, res) => {
  if (req.userRole !== 'RENTER') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only renters can return rentals'));
  }
  
  const rental = storage.rentals.get(req.params.rental_id);
  if (!rental || rental.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_RENTAL_NOT_FOUND', 'Rental not found'));
  }
  
  if (rental.renter_id !== req.userId) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Cannot return another user\'s rental'));
  }
  
  if (rental.state !== 'ACTIVE') {
    return res.status(409).json(createErrorResponse('ERR_RENTAL_NOT_ACTIVE', 'Rental is not active'));
  }
  
  const { end_odometer_mi, end_zone_id, damage_grade, cleanliness_grade } = req.body;
  
  const endZone = storage.parkingZones.get(end_zone_id);
  if (!endZone || endZone.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_ZONE_NOT_FOUND', 'End zone not found'));
  }
  
  if (endZone.lock_state === 'HARD_LOCK') {
    return res.status(409).json(createErrorResponse('ERR_ZONE_LOCKED_FOR_RETURN', 'Zone is hard locked'));
  }
  
  if (end_odometer_mi < rental.start_odometer_mi) {
    return res.status(422).json(createErrorResponse('ERR_ODOMETER_ROLLBACK', 'Odometer cannot decrease'));
  }
  
  const conditionReport = {
    id: generateId('report'),
    fleet_id: req.params.fleet_id,
    vehicle_id: rental.vehicle_id,
    reporter_id: req.userId,
    report_type: 'RETURN',
    damage_grade,
    cleanliness_grade,
    created_at: generateTimestamp()
  };
  
  storage.conditionReports.set(conditionReport.id, conditionReport);
  rental.return_condition_report_id = conditionReport.id;
  
  const vehicle = storage.vehicles.get(rental.vehicle_id);
  const milesDriven = end_odometer_mi - rental.start_odometer_mi;
  
  const mileageLedger = storage.vehicleMileageLedger.get(rental.vehicle_id);
  let effectiveMiles = milesDriven;
  
  if (mileageLedger && mileageLedger.override_miles_credit > 0) {
    effectiveMiles = Math.max(0, milesDriven - mileageLedger.override_miles_credit);
    mileageLedger.override_miles_credit = 0;
  }
  
  updateVehicleMileage(rental.vehicle_id, effectiveMiles);
  
  const currentActionCount = getFleetActionCounter(req.params.fleet_id);
  const isLate = currentActionCount > rental.start_action_count + rental.quoted_action_duration;
  
  const repLedger = storage.renterReputationLedger.get(req.userId);
  const user = storage.users.get(req.userId);
  
  if (isLate && repLedger) {
    repLedger.late_counter++;
    user.late_return_history.push(rental.id);
    if (user.late_return_history.length > 5) {
      user.late_return_history.shift();
    }
    
    if (repLedger.late_counter === 1) {
      updateRenterReputation(req.userId, -5);
    } else if (repLedger.late_counter <= 3) {
      updateRenterReputation(req.userId, -15);
    } else {
      updateRenterReputation(req.userId, -30);
      user.suspended = true;
    }
  }
  
  if (endZone.incentivized_classes.includes(vehicle.class)) {
    updateRenterReputation(req.userId, 5);
  } else if (endZone.discouraged_classes.includes(vehicle.class)) {
    updateRenterReputation(req.userId, -5);
  }
  
  vehicle.odometer_mi = end_odometer_mi;
  vehicle.current_zone_id = end_zone_id;
  vehicle.miles_until_service = calculateMilesUntilService(vehicle);
  
  updateZoneBalance(end_zone_id, 1);
  
  if (vehicle.flags.includes('MAINT_OVERRIDE_GRANTED')) {
    vehicle.flags = vehicle.flags.filter(f => f !== 'MAINT_OVERRIDE_GRANTED');
    const activeHold = Array.from(storage.maintenanceHolds.values())
      .find(h => h.vehicle_id === rental.vehicle_id && h.active && h.override_allowed);
    if (activeHold) {
      activeHold.active = false;
      vehicle.flags = vehicle.flags.filter(f => f !== 'MAINT_HOLD');
    }
    vehicle.state = 'NEEDS_MAINTENANCE';
  } else if (damage_grade === 'MAJOR' || calculateMilesUntilService(vehicle) <= 0) {
    vehicle.state = 'NEEDS_MAINTENANCE';
  } else if (cleanliness_grade === 'POOR') {
    vehicle.state = 'NEEDS_CLEANING';
    if (!vehicle.flags.includes('CLEAN_REQ')) {
      vehicle.flags.push('CLEAN_REQ');
    }
  } else {
    vehicle.state = 'AVAILABLE';
  }
  
  if (isLate || damage_grade === 'MAJOR') {
    rental.state = 'INFRACTION_REVIEW';
  } else {
    rental.state = 'COMPLETED';
    user.active_rental_id = null;
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse(rental));
});

// PATCH /city-fleets/{fleet_id}/rentals/{rental_id}/review
app.patch('/city-fleets/:fleet_id/rentals/:rental_id/review', authenticateUser, (req, res) => {
  if (req.userRole !== 'FLEET_OPERATOR') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only fleet operators can review rentals'));
  }
  
  const rental = storage.rentals.get(req.params.rental_id);
  if (!rental || rental.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_RENTAL_NOT_FOUND', 'Rental not found'));
  }
  
  if (rental.state !== 'INFRACTION_REVIEW') {
    return res.status(409).json(createErrorResponse('ERR_RENTAL_NOT_IN_REVIEW', 'Rental is not in review'));
  }
  
  rental.state = 'COMPLETED';
  
  const user = storage.users.get(rental.renter_id);
  if (user) {
    user.active_rental_id = null;
  }
  
  incrementFleetActionCounter(req.params.fleet_id);
  res.json(createResponse({ message: 'Rental review completed' }));
});

// GET /city-fleets/{fleet_id}/users/{user_id}
app.get('/city-fleets/:fleet_id/users/:user_id', authenticateUser, (req, res) => {
  const user = storage.users.get(req.params.user_id);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_USER_NOT_FOUND', 'User not found'));
  }
  
  const requestingUser = storage.users.get(req.userId);
  if (!requestingUser || requestingUser.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  res.json(createResponse(user));
});

// GET /city-fleets/{fleet_id}/parking-zones/{zone_id}
app.get('/city-fleets/:fleet_id/parking-zones/:zone_id', authenticateUser, (req, res) => {
  const zone = storage.parkingZones.get(req.params.zone_id);
  if (!zone || zone.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_ZONE_NOT_FOUND', 'Zone not found'));
  }
  
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  const zoneData = { ...zone };
  
  if (req.userRole === 'RENTER') {
    delete zoneData.target_vehicles;
    delete zoneData.manual_lock;
  }
  
  res.json(createResponse(zoneData));
});

// GET /city-fleets/{fleet_id}/vehicles/{vehicle_id}
app.get('/city-fleets/:fleet_id/vehicles/:vehicle_id', authenticateUser, (req, res) => {
  const vehicle = storage.vehicles.get(req.params.vehicle_id);
  if (!vehicle || vehicle.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_VEHICLE_NOT_FOUND', 'Vehicle not found'));
  }
  
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  const vehicleData = { ...vehicle };
  vehicleData.miles_until_service = calculateMilesUntilService(vehicle);
  
  res.json(createResponse(vehicleData));
});

// GET /city-fleets/{fleet_id}/rentals/{rental_id}
app.get('/city-fleets/:fleet_id/rentals/:rental_id', authenticateUser, (req, res) => {
  const rental = storage.rentals.get(req.params.rental_id);
  if (!rental || rental.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_RENTAL_NOT_FOUND', 'Rental not found'));
  }
  
  if (req.userRole === 'RENTER' && rental.renter_id !== req.userId) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Cannot view another user\'s rental'));
  }
  
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  res.json(createResponse(rental));
});

// GET /city-fleets/{fleet_id}/condition-reports/{report_id}
app.get('/city-fleets/:fleet_id/condition-reports/:report_id', authenticateUser, (req, res) => {
  const report = storage.conditionReports.get(req.params.report_id);
  if (!report || report.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_REPORT_NOT_FOUND', 'Report not found'));
  }
  
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  res.json(createResponse(report));
});

// GET /city-fleets/{fleet_id}/maintenance-holds
app.get('/city-fleets/:fleet_id/maintenance-holds', authenticateUser, (req, res) => {
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  let holds = Array.from(storage.maintenanceHolds.values())
    .filter(h => h.fleet_id === req.params.fleet_id);
  
  if (req.userRole !== 'FLEET_OPERATOR') {
    holds = holds.filter(h => h.active);
  }
  
  res.json(createResponse(holds, 'array'));
});

// GET /city-fleets/{fleet_id}/maintenance-holds/{hold_id}
app.get('/city-fleets/:fleet_id/maintenance-holds/:hold_id', authenticateUser, (req, res) => {
  const hold = storage.maintenanceHolds.get(req.params.hold_id);
  if (!hold || hold.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_HOLD_NOT_FOUND', 'Hold not found'));
  }
  
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  res.json(createResponse(hold));
});

// GET /city-fleets/{fleet_id}/ledgers/zone-balance
app.get('/city-fleets/:fleet_id/ledgers/zone-balance', authenticateUser, (req, res) => {
  if (req.userRole !== 'FLEET_OPERATOR') {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Only fleet operators can view zone balance'));
  }
  
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  const balances = [];
  for (const [zoneId, balance] of storage.zoneBalanceLedger) {
    const zone = storage.parkingZones.get(zoneId);
    if (zone && zone.fleet_id === req.params.fleet_id) {
      balances.push({
        zone_id: zoneId,
        zone_code: zone.code,
        surplus: balance.surplus
      });
    }
  }
  
  res.json(createResponse(balances, 'array'));
});

// GET /city-fleets/{fleet_id}/ledgers/renter/{renter_id}/reputation
app.get('/city-fleets/:fleet_id/ledgers/renter/:renter_id/reputation', authenticateUser, (req, res) => {
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  if (req.userRole === 'RENTER' && req.userId !== req.params.renter_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'Cannot view another user\'s reputation'));
  }
  
  const renter = storage.users.get(req.params.renter_id);
  if (!renter || renter.fleet_id !== req.params.fleet_id || renter.role !== 'RENTER') {
    return res.status(404).json(createErrorResponse('ERR_RENTER_NOT_FOUND', 'Renter not found'));
  }
  
  const repLedger = storage.renterReputationLedger.get(req.params.renter_id);
  if (!repLedger) {
    return res.status(404).json(createErrorResponse('ERR_LEDGER_NOT_FOUND', 'Reputation ledger not found'));
  }
  
  res.json(createResponse({
    renter_id: req.params.renter_id,
    reputation_score: repLedger.reputation_score,
    late_counter: repLedger.late_counter
  }));
});

// GET /city-fleets/{fleet_id}/ledgers/vehicle/{vehicle_id}/mileage
app.get('/city-fleets/:fleet_id/ledgers/vehicle/:vehicle_id/mileage', authenticateUser, (req, res) => {
  const user = storage.users.get(req.userId);
  if (!user || user.fleet_id !== req.params.fleet_id) {
    return res.status(403).json(createErrorResponse('ERR_FORBIDDEN', 'User not in fleet'));
  }
  
  const vehicle = storage.vehicles.get(req.params.vehicle_id);
  if (!vehicle || vehicle.fleet_id !== req.params.fleet_id) {
    return res.status(404).json(createErrorResponse('ERR_VEHICLE_NOT_FOUND', 'Vehicle not found'));
  }
  
  const mileageLedger = storage.vehicleMileageLedger.get(req.params.vehicle_id);
  if (!mileageLedger) {
    return res.status(404).json(createErrorResponse('ERR_LEDGER_NOT_FOUND', 'Mileage ledger not found'));
  }
  
  res.json(createResponse({
    vehicle_id: req.params.vehicle_id,
    miles_since_service: vehicle.odometer_mi - vehicle.last_service_odometer_mi,
    override_miles_credit: mileageLedger.override_miles_credit
  }));
});

// 404 handler
app.use((req, res) => {
  res.status(404).json(createErrorResponse('ERR_ENDPOINT_NOT_FOUND', 'Endpoint not found'));
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Car-share API server running on port ${PORT}`);
});