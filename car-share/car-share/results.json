{"numFailedTestSuites": 4, "numFailedTests": 84, "numPassedTestSuites": 0, "numPassedTests": 29, "numPendingTestSuites": 0, "numPendingTests": 1, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 4, "numTotalTests": 114, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1751261096431, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Hierarchy of Truth - Precedence Tests", "PREC-1: Suspended user vs. vehicle not available"], "duration": 22, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:48:38)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Hierarchy of Truth - Precedence Tests PREC-1: Suspended user vs. vehicle not available should return ERR_USER_SUSPENDED before checking vehicle state", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return ERR_USER_SUSPENDED before checking vehicle state"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Hierarchy of Truth - Precedence Tests", "PREC-2: Vehicle not available vs. zone locked"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:77:38)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Hierarchy of Truth - Precedence Tests PREC-2: Vehicle not available vs. zone locked should return ERR_VEHICLE_NOT_RENTABLE before checking zone lock", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return ERR_VEHICLE_NOT_RENTABLE before checking zone lock"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Hierarchy of Truth - Precedence Tests", "PREC-3: Manual zone lock vs. rep too low"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.setupLowRepRenter (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:391:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:100:30)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Hierarchy of Truth - Precedence Tests PREC-3: Manual zone lock vs. rep too low should return ERR_ZONE_LOCKED_FOR_RENTAL before checking reputation", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return ERR_ZONE_LOCKED_FOR_RENTAL before checking reputation"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Hierarchy of Truth - Precedence Tests", "PREC-4: Hard lock return vs. odometer rollback"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:120:26)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Hierarchy of Truth - Precedence Tests PREC-4: Hard lock return vs. odometer rollback should return ERR_ZONE_LOCKED_FOR_RETURN before checking odometer", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return ERR_ZONE_LOCKED_FOR_RETURN before checking odometer"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Hierarchy of Truth - Precedence Tests", "PREC-5: Odometer rollback vs. low reputation penalty"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:149:26)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Hierarchy of Truth - Precedence Tests PREC-5: Odometer rollback vs. low reputation penalty should return ERR_ODOMETER_ROLLBACK before considering late return penalties", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return ERR_ODOMETER_ROLLBACK before considering late return penalties"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Hierarchy of Truth - Precedence Tests", "PREC-6: User suspension vs. luxury lock"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Hierarchy of Truth - Precedence Tests PREC-6: User suspension vs. luxury lock should return ERR_USER_SUSPENDED even for luxury vehicle access", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should return ERR_USER_SUSPENDED even for luxury vehicle access"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "End-to-End Tests", "E2E-1: Standard rental happy path"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: Expected status 201, got 500\n    at handleApiResponse (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:200:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:221:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests End-to-End Tests E2E-1: Standard rental happy path should complete a successful rental with all state transitions", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should complete a successful rental with all state transitions"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "End-to-End Tests", "E2E-3: Maintenance override rental flow"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.setupHighRepRenter (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:372:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:269:31)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests End-to-End Tests E2E-3: Maintenance override rental flow should handle maintenance override rental correctly", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle maintenance override rental correctly"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "End-to-End Tests", "E2E-4: Infraction scenario with damage"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:331:26)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests End-to-End Tests E2E-4: Infraction scenario with damage should handle return with major damage and infraction review", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle return with major damage and infraction review"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "End-to-End Tests", "E2E-5: Cross-role interactions and cleanup"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:397:38)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests End-to-End Tests E2E-5: Cross-role interactions and cleanup should handle complex multi-role scenario with zone locks", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle complex multi-role scenario with zone locks"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Validation Tests", "VAL-1: Reject invalid City Code format"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Validation Tests VAL-1: Reject invalid City Code format should reject city code with numbers", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should reject city code with numbers"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Validation Tests", "VAL-2: Reject creating user with too short name"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Validation Tests VAL-2: Reject creating user with too short name should reject user with name too short", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should reject user with name too short"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Validation Tests", "VAL-3: Reject user with invalid email format"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Validation Tests VAL-3: Reject user with invalid email format should reject malformed email", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should reject malformed email"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Validation Tests", "VAL-4: Reject license_plate with invalid characters"], "duration": 2, "failureDetails": [{"matcherResult": {"actual": 400, "expected": 422, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m422\u001b[39m\nReceived: \u001b[31m400\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m422\u001b[39m\nReceived: \u001b[31m400\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:496:33)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Validation Tests VAL-4: Reject license_plate with invalid characters should reject license plate with invalid characters", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject license plate with invalid characters"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Validation Tests", "VAL-5: Reject VIN with incorrect length"], "duration": 1, "failureDetails": [{"matcherResult": {"actual": 400, "expected": 422, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m422\u001b[39m\nReceived: \u001b[31m400\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m422\u001b[39m\nReceived: \u001b[31m400\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:511:33)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Validation Tests VAL-5: Reject VIN with incorrect length should reject VIN that is too short", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject VIN that is too short"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Validation Tests", "VAL-6: Battery percentage required for EV"], "duration": 1, "failureDetails": [{"matcherResult": {"actual": 400, "expected": 422, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m422\u001b[39m\nReceived: \u001b[31m400\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m422\u001b[39m\nReceived: \u001b[31m400\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:527:33)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Validation Tests VAL-6: Battery percentage required for EV should reject EV without battery_pct", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject EV without battery_pct"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Validation Tests", "VAL-7: Prevent duplicate zone code"], "duration": 1, "failureDetails": [{"matcherResult": {"actual": 201, "expected": 409, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m409\u001b[39m\nReceived: \u001b[31m201\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m409\u001b[39m\nReceived: \u001b[31m201\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:542:33)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Validation Tests VAL-7: Prevent duplicate zone code should reject duplicate zone codes", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject duplicate zone codes"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Validation Tests", "VAL-8: Reserved zone code prefix \"EA-\" is blocked"], "duration": 1, "failureDetails": [{}], "failureMessages": ["Error: Expected error_id \"ERR_RESERVED_ZONE_CODE\", but response contained \"ERR_INVALID_ZONE_CODE\"\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:129:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:553:30)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Validation Tests VAL-8: Reserved zone code prefix \"EA-\" is blocked should reject zone codes starting with EA-", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject zone codes starting with EA-"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests"], "duration": 4, "failureDetails": [{"matcherResult": {"actual": "OPEN", "expected": "SOFT_LOCK", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"SOFT_LOCK\"\u001b[39m\nReceived: \u001b[31m\"OPEN\"\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"SOFT_LOCK\"\u001b[39m\nReceived: \u001b[31m\"OPEN\"\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:579:36)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests MZL-2: Zone returns to automatic control after manual_lock released", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "MZL-2: Zone returns to automatic control after manual_lock released"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: Expected error_id \"ERR_FORBIDDEN\", but response contained \"ERR_VEHICLE_NOT_DIRTY\"\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:129:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:608:26)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests XFL-1: Cannot modify resources across fleet boundaries", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "XFL-1: Cannot modify resources across fleet boundaries"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "RBAC Field Redaction Tests"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests RBAC Field Redaction Tests RBAC-1: <PERSON><PERSON> gets full vehicle details as specified", "invocations": 1, "location": null, "numPassingAsserts": 10, "retryReasons": [], "status": "passed", "title": "RBAC-1: <PERSON><PERSON> gets full vehicle details as specified"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "RBAC Field Redaction Tests"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests RBAC Field Redaction Tests RBAC-2: <PERSON><PERSON> cannot access fleet-level ledgers", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "RBAC-2: <PERSON><PERSON> cannot access fleet-level ledgers"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "RBAC Field Redaction Tests"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests RBAC Field Redaction Tests RBAC-3: Mechanic cannot use Fleet Operator endpoints", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "RBAC-3: Mechanic cannot use Fleet Operator endpoints"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Ledger Integrity Tests"], "duration": 1, "failureDetails": [{"matcherResult": {"expected": 100, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m100\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m100\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:660:39)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Ledger Integrity Tests LEDG-1: Renter Reputation Ledger entry auto-created on user creation", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "LEDG-1: Renter Reputation Ledger entry auto-created on user creation"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Edge Cases Tests"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:669:26)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Edge Cases Tests EDGE-1: Zero-activity rental duration check", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "EDGE-1: Zero-activity rental duration check"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Zone Threshold Clarity Tests"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: Unknown error_id \"ERR_VEHICLE_NOT_FOUND\" received. Not found in HTTP_STATUS_CODES map.\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:135:11)\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:164:7)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:709:37)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Zone Threshold Clarity Tests ZTHRESH-1: Zone enters SOFT_LOCK exactly at surplus -5", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "ZTHRESH-1: Zone enters SOFT_LOCK exactly at surplus -5"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Zone Threshold Clarity Tests"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: Unknown error_id \"ERR_VEHICLE_NOT_FOUND\" received. Not found in HTTP_STATUS_CODES map.\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:135:11)\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:164:7)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:743:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Zone Threshold Clarity Tests ZTHRESH-2: Zone remains SOFT_LOCK at surplus -6", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "ZTHRESH-2: Zone remains SOFT_LOCK at surplus -6"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Emergency Management Tests"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_QUOTE_EXISTS - User already has active quote\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:790:36)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Emergency Management Tests EMG-1: Fleet phase escalation affects new quotes but not existing ones", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "EMG-1: Fleet phase escalation affects new quotes but not existing ones"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Emergency Management Tests"], "duration": 4, "failureDetails": [{"matcherResult": {"actual": "HARD_LOCK", "expected": "SOFT_LOCK", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"\u001b[7mSOFT\u001b[27m_LOCK\"\u001b[39m\nReceived: \u001b[31m\"\u001b[7mHARD\u001b[27m_LOCK\"\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"\u001b[7mSOFT\u001b[27m_LOCK\"\u001b[39m\nReceived: \u001b[31m\"\u001b[7mHARD\u001b[27m_LOCK\"\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:820:38)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Emergency Management Tests EMG-2: Manual lock release triggers automatic re-evaluation", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "EMG-2: Manual lock release triggers automatic re-evaluation"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Emergency Management Tests"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:824:26)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Emergency Management Tests EMG-3: Suspended renter can return vehicle but not start new rental", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "EMG-3: Suspended renter can return vehicle but not start new rental"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Emergency Management Tests"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_ZONE_LOCKED_FOR_RENTAL - Start zone is locked\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:868:35)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Emergency Management Tests EMG-5: Price validation uses stored hash not fresh pricing", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "EMG-5: Price validation uses stored hash not fresh pricing"}, {"ancestorTitles": ["Test Suite 04 - Precedence, E2E, and Validation Tests", "Emergency Management Tests"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts:897:26)"], "fullName": "Test Suite 04 - Precedence, E2E, and Validation Tests Emergency Management Tests EMG-6: Active rental block persists after suspension lifted", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "EMG-6: Active rental block persists after suspension lifted"}], "endTime": 1751261096700, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Hierarchy of Truth - Precedence Tests › PREC-1: Suspended user vs. vehicle not available › should return ERR_USER_SUSPENDED before checking vehicle state\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:48:38)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Hierarchy of Truth - Precedence Tests › PREC-2: Vehicle not available vs. zone locked › should return ERR_VEHICLE_NOT_RENTABLE before checking zone lock\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:77:38)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Hierarchy of Truth - Precedence Tests › PREC-3: Manual zone lock vs. rep too low › should return ERR_ZONE_LOCKED_FOR_RENTAL before checking reputation\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupLowRepRenter (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:391:41)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:100:30)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Hierarchy of Truth - Precedence Tests › PREC-4: Hard lock return vs. odometer rollback › should return ERR_ZONE_LOCKED_FOR_RETURN before checking odometer\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:120:26)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Hierarchy of Truth - Precedence Tests › PREC-5: Odometer rollback vs. low reputation penalty › should return ERR_ODOMETER_ROLLBACK before considering late return penalties\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:149:26)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › End-to-End Tests › E2E-1: Standard rental happy path › should complete a successful rental with all state transitions\u001b[39m\u001b[22m\n\n    Expected status 201, got 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 198 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 199 |\u001b[39m     }\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 200 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 201 |\u001b[39m       \u001b[32m`Expected status ${statusArray.join(' or ')}, got ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 202 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 203 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat handleApiResponse (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:200:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:221:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › End-to-End Tests › E2E-3: Maintenance override rental flow › should handle maintenance override rental correctly\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupHighRepRenter (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:372:41)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:269:31)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › End-to-End Tests › E2E-4: Infraction scenario with damage › should handle return with major damage and infraction review\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:331:26)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › End-to-End Tests › E2E-5: Cross-role interactions and cleanup › should handle complex multi-role scenario with zone locks\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:397:38)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Validation Tests › VAL-4: Reject license_plate with invalid characters › should reject license plate with invalid characters\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m422\u001b[39m\n    Received: \u001b[31m400\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 494 |\u001b[39m         \u001b[36mconst\u001b[39m response \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m apiClient\u001b[33m.\u001b[39mcreateVehicle(testContext\u001b[33m.\u001b[39mfleetId\u001b[33m,\u001b[39m invalidVehiclePayload\u001b[33m,\u001b[39m mechanicHeaders)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 495 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 496 |\u001b[39m         expect(response\u001b[33m.\u001b[39mstatus)\u001b[33m.\u001b[39mtoBe(\u001b[35m422\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 497 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 498 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 499 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:496:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Validation Tests › VAL-5: Reject VIN with incorrect length › should reject VIN that is too short\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m422\u001b[39m\n    Received: \u001b[31m400\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 509 |\u001b[39m         \u001b[36mconst\u001b[39m response \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m apiClient\u001b[33m.\u001b[39mcreateVehicle(testContext\u001b[33m.\u001b[39mfleetId\u001b[33m,\u001b[39m invalidVehiclePayload\u001b[33m,\u001b[39m mechanicHeaders)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 510 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 511 |\u001b[39m         expect(response\u001b[33m.\u001b[39mstatus)\u001b[33m.\u001b[39mtoBe(\u001b[35m422\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 512 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 513 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 514 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:511:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Validation Tests › VAL-6: Battery percentage required for EV › should reject EV without battery_pct\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m422\u001b[39m\n    Received: \u001b[31m400\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 525 |\u001b[39m         \u001b[36mconst\u001b[39m response \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m apiClient\u001b[33m.\u001b[39mcreateVehicle(testContext\u001b[33m.\u001b[39mfleetId\u001b[33m,\u001b[39m invalidEVPayload\u001b[33m,\u001b[39m mechanicHeaders)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 526 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 527 |\u001b[39m         expect(response\u001b[33m.\u001b[39mstatus)\u001b[33m.\u001b[39mtoBe(\u001b[35m422\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 528 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 529 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 530 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:527:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Validation Tests › VAL-7: Prevent duplicate zone code › should reject duplicate zone codes\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m409\u001b[39m\n    Received: \u001b[31m201\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 540 |\u001b[39m         \u001b[36mconst\u001b[39m response \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m apiClient\u001b[33m.\u001b[39mcreateParkingZone(testContext\u001b[33m.\u001b[39mfleetId\u001b[33m,\u001b[39m duplicateZonePayload\u001b[33m,\u001b[39m operatorHeaders)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 541 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 542 |\u001b[39m         expect(response\u001b[33m.\u001b[39mstatus)\u001b[33m.\u001b[39mtoBe(\u001b[35m409\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 543 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 544 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 545 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:542:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Validation Tests › VAL-8: Reserved zone code prefix \"EA-\" is blocked › should reject zone codes starting with EA-\u001b[39m\u001b[22m\n\n    Expected error_id \"ERR_RESERVED_ZONE_CODE\", but response contained \"ERR_INVALID_ZONE_CODE\"\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 127 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 128 |\u001b[39m   \u001b[36mif\u001b[39m (options\u001b[33m.\u001b[39mexpectedErrorId \u001b[33m&&\u001b[39m actualErrorIdFromResponse \u001b[33m!==\u001b[39m options\u001b[33m.\u001b[39mexpectedErrorId) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 129 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Expected error_id \"${options.expectedErrorId}\", but response contained \"${actualErrorIdFromResponse}\"`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 130 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 131 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 132 |\u001b[39m   \u001b[36mconst\u001b[39m expectedHttpStatusFromMap \u001b[33m=\u001b[39m \u001b[33mHTTP_STATUS_CODES\u001b[39m[actualErrorIdFromResponse]\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:129:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:553:30)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › MZL-2: Zone returns to automatic control after manual_lock released\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"SOFT_LOCK\"\u001b[39m\n    Received: \u001b[31m\"OPEN\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 577 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 578 |\u001b[39m     \u001b[36mconst\u001b[39m updatedZone \u001b[33m=\u001b[39m getSuccessData(releaseResponse)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 579 |\u001b[39m     expect(updatedZone\u001b[33m.\u001b[39mlock_state)\u001b[33m.\u001b[39mtoBe(\u001b[33mZoneLockState\u001b[39m\u001b[33m.\u001b[39m\u001b[33mSOFT_LOCK\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 580 |\u001b[39m     expect(updatedZone\u001b[33m.\u001b[39mmanual_lock)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 581 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 582 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:579:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › XFL-1: Cannot modify resources across fleet boundaries\u001b[39m\u001b[22m\n\n    Expected error_id \"ERR_FORBIDDEN\", but response contained \"ERR_VEHICLE_NOT_DIRTY\"\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 127 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 128 |\u001b[39m   \u001b[36mif\u001b[39m (options\u001b[33m.\u001b[39mexpectedErrorId \u001b[33m&&\u001b[39m actualErrorIdFromResponse \u001b[33m!==\u001b[39m options\u001b[33m.\u001b[39mexpectedErrorId) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 129 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Expected error_id \"${options.expectedErrorId}\", but response contained \"${actualErrorIdFromResponse}\"`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 130 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 131 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 132 |\u001b[39m   \u001b[36mconst\u001b[39m expectedHttpStatusFromMap \u001b[33m=\u001b[39m \u001b[33mHTTP_STATUS_CODES\u001b[39m[actualErrorIdFromResponse]\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:129:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:608:26)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Ledger Integrity Tests › LEDG-1: Renter Reputation Ledger entry auto-created on user creation\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m100\u001b[39m\n    Received: \u001b[31mundefined\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 658 |\u001b[39m       \u001b[36mconst\u001b[39m renter \u001b[33m=\u001b[39m getSuccessData(response)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 659 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 660 |\u001b[39m       expect(renter\u001b[33m.\u001b[39mreputation_score)\u001b[33m.\u001b[39mtoBe(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 661 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 662 |\u001b[39m       \u001b[36mconst\u001b[39m ledgerResponse \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m apiClient\u001b[33m.\u001b[39mgetRenterReputationLedger(testContext\u001b[33m.\u001b[39mfleetId\u001b[33m,\u001b[39m renter\u001b[33m.\u001b[39mid\u001b[33m,\u001b[39m operatorHeaders)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 663 |\u001b[39m       \u001b[36mconst\u001b[39m ledgerEntry \u001b[33m=\u001b[39m getSuccessData(ledgerResponse)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:660:39)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Edge Cases Tests › EDGE-1: Zero-activity rental duration check\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:669:26)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Zone Threshold Clarity Tests › ZTHRESH-1: Zone enters SOFT_LOCK exactly at surplus -5\u001b[39m\u001b[22m\n\n    Unknown error_id \"ERR_VEHICLE_NOT_FOUND\" received. Not found in HTTP_STATUS_CODES map.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 133 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 134 |\u001b[39m   \u001b[36mif\u001b[39m (expectedHttpStatusFromMap \u001b[33m===\u001b[39m undefined) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 135 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Unknown error_id \"${actualErrorIdFromResponse}\" received. Not found in HTTP_STATUS_CODES map.`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 136 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 137 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 138 |\u001b[39m   \u001b[36mif\u001b[39m (actualHttpStatus \u001b[33m!==\u001b[39m expectedHttpStatusFromMap) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:135:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:164:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:709:37)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Zone Threshold Clarity Tests › ZTHRESH-2: Zone remains SOFT_LOCK at surplus -6\u001b[39m\u001b[22m\n\n    Unknown error_id \"ERR_VEHICLE_NOT_FOUND\" received. Not found in HTTP_STATUS_CODES map.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 133 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 134 |\u001b[39m   \u001b[36mif\u001b[39m (expectedHttpStatusFromMap \u001b[33m===\u001b[39m undefined) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 135 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Unknown error_id \"${actualErrorIdFromResponse}\" received. Not found in HTTP_STATUS_CODES map.`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 136 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 137 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 138 |\u001b[39m   \u001b[36mif\u001b[39m (actualHttpStatus \u001b[33m!==\u001b[39m expectedHttpStatusFromMap) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:135:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:164:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:743:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Emergency Management Tests › EMG-1: Fleet phase escalation affects new quotes but not existing ones\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_QUOTE_EXISTS - User already has active quote\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:790:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Emergency Management Tests › EMG-2: Manual lock release triggers automatic re-evaluation\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"\u001b[7mSOFT\u001b[27m_LOCK\"\u001b[39m\n    Received: \u001b[31m\"\u001b[7mHARD\u001b[27m_LOCK\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 818 |\u001b[39m       \u001b[36mconst\u001b[39m updatedZone \u001b[33m=\u001b[39m getSuccessData(releaseResponse)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 819 |\u001b[39m       expect(updatedZone\u001b[33m.\u001b[39mmanual_lock)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 820 |\u001b[39m       expect(updatedZone\u001b[33m.\u001b[39mlock_state)\u001b[33m.\u001b[39mtoBe(\u001b[33mZoneLockState\u001b[39m\u001b[33m.\u001b[39m\u001b[33mSOFT_LOCK\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 821 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 822 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 823 |\u001b[39m     it(\u001b[32m'EMG-3: Suspended renter can return vehicle but not start new rental'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:820:38)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Emergency Management Tests › EMG-3: Suspended renter can return vehicle but not start new rental\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:824:26)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Emergency Management Tests › EMG-5: Price validation uses stored hash not fresh pricing\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_ZONE_LOCKED_FOR_RENTAL - Start zone is locked\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:868:35)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTest Suite 04 - Precedence, E2E, and Validation Tests › Emergency Management Tests › EMG-6: Active rental block persists after suspension lifted\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/04.test.ts\u001b[39m\u001b[0m\u001b[2m:897:26)\u001b[22m\u001b[2m\u001b[22m\n", "name": "/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/04.test.ts", "startTime": 1751261096449, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Business Rule Validation Tests", "BR-1: Suspended user cannot initiate quote"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-1: Suspended user cannot initiate quote should reject quote creation by suspended user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject quote creation by suspended user"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-2: User with existing active rental cannot create a new quote"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:44:33)"], "fullName": "Business Rule Validation Tests BR-2: User with existing active rental cannot create a new quote should reject quote creation by user with active rental", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject quote creation by user with active rental"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-3: Prevent second active quote for same renter"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-3: Prevent second active quote for same renter should reject duplicate quote attempt", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject duplicate quote attempt"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-4: Vehicle not rentable due to state"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:74:33)"], "fullName": "Business Rule Validation Tests BR-4: Vehicle not rentable due to state should reject quote for IN_USE vehicle", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject quote for IN_USE vehicle"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-5: Vehicle not rentable due to low EV battery"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-5: Vehicle not rentable due to low EV battery should reject quote for EV with battery < 20%", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject quote for EV with battery < 20%"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-6: Vehicle not rentable due to required maintenance"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: Unknown error_id \"ERR_INVALID_LAST_SERVICE_ODOMETER\" received. Not found in HTTP_STATUS_CODES map.\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:135:11)\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:164:7)\n    at Object.createTestVehicle (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:214:26)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:105:34)"], "fullName": "Business Rule Validation Tests BR-6: Vehicle not rentable due to required maintenance should reject quote for vehicle due for service", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject quote for vehicle due for service"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-7: Override rental blocked by insufficient rep"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.setupLowRepRenter (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:391:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:123:28)"], "fullName": "Business Rule Validation Tests BR-7: Override rental blocked by insufficient rep should reject override attempt with low reputation", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject override attempt with low reputation"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-8: Override rental blocked by multiple holds"], "duration": 1, "failureDetails": [{}], "failureMessages": ["Error: Unknown error_id \"ERR_INVALID_LAST_SERVICE_ODOMETER\" received. Not found in HTTP_STATUS_CODES map.\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:135:11)\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:164:7)\n    at Object.createTestVehicle (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:214:26)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:138:23)"], "fullName": "Business Rule Validation Tests BR-8: Override rental blocked by multiple holds should reject override attempt with multiple holds", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject override attempt with multiple holds"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-9: Override rental blocked by non-overridable hold"], "duration": 1, "failureDetails": [{}], "failureMessages": ["Error: Unknown error_id \"ERR_INVALID_LAST_SERVICE_ODOMETER\" received. Not found in HTTP_STATUS_CODES map.\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:135:11)\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:164:7)\n    at Object.createTestVehicle (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:214:26)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:171:23)"], "fullName": "Business Rule Validation Tests BR-9: Override rental blocked by non-overridable hold should reject override attempt with non-overridable hold", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject override attempt with non-overridable hold"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-10: Override quote rejected if distance estimate exceeds allowed miles"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.setupHighRepRenter (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:372:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:197:29)"], "fullName": "Business Rule Validation Tests BR-10: Override quote rejected if distance estimate exceeds allowed miles should reject override when distance exceeds allowed miles", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject override when distance exceeds allowed miles"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-11: Vehicle-zone mismatch blocks quote"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-11: Vehicle-zone mismatch blocks quote should reject quote when vehicle not in specified start zone", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject quote when vehicle not in specified start zone"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-12: Zone locked (SOFT/HARD) blocks new rental quote"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-12: Zone locked (SOFT/HARD) blocks new rental quote should reject quote for vehicle in HARD_LOCK zone", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject quote for vehicle in HARD_LOCK zone"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-13: Missing quote_id on rental confirmation"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-13: Missing quote_id on rental confirmation should reject rental confirmation without quote_id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject rental confirmation without quote_id"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-14: Quote tampering detected on rental confirmation"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: Response envelope must be an object\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:96:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:265:28)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Business Rule Validation Tests BR-14: Quote tampering detected on rental confirmation should reject rental confirmation with tampered price", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject rental confirmation with tampered price"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-15: Prevent cancellation of non-confirmed rental"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:271:33)"], "fullName": "Business Rule Validation Tests BR-15: Prevent cancellation of non-confirmed rental should reject cancellation of active rental", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject cancellation of active rental"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-16: Prevent return on a rental that is not active"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:295:36)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Business Rule Validation Tests BR-16: Prevent return on a rental that is not active should reject return on confirmed but not active rental", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject return on confirmed but not active rental"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-17: Return fails if destination zone does not exist"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:306:33)"], "fullName": "Business Rule Validation Tests BR-17: Return fails if destination zone does not exist should reject return to non-existent zone", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject return to non-existent zone"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-18: Return fails if destination zone is HARD_LOCK"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:319:33)"], "fullName": "Business Rule Validation Tests BR-18: Return fails if destination zone is HARD_LOCK should reject return to HARD_LOCK zone", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject return to HARD_LOCK zone"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-19: Re<PERSON> return with odometer rollback"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:332:33)"], "fullName": "Business Rule Validation Tests BR-19: Reject return with odometer rollback should reject return with end odometer < start odometer", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject return with end odometer < start odometer"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-20: Mechanic cannot charge a non-EV vehicle"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-20: Mechanic cannot charge a non-EV vehicle should reject charge attempt on non-EV vehicle", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject charge attempt on non-EV vehicle"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-21: Mechanic cannot service a vehicle not in maintenance state"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-21: Mechanic cannot service a vehicle not in maintenance state should reject service on non-maintenance vehicle", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject service on non-maintenance vehicle"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-22: Operator cannot mark clean a vehicle that isn't dirty"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-22: Operator cannot mark clean a vehicle that isn't dirty should reject clean complete on non-dirty vehicle", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject clean complete on non-dirty vehicle"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-23: Cannot release a maintenance hold that's already inactive"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-23: Cannot release a maintenance hold that's already inactive should reject release of already released hold", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject release of already released hold"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-24: Reject creating vehicle of class not allowed in fleet"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-24: Reject creating vehicle of class not allowed in fleet should reject vehicle creation with disallowed class", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject vehicle creation with disallowed class"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-25: New vehicle must have an initial zone assignment"], "duration": 1, "failureDetails": [{}], "failureMessages": ["Error: Expected error_id \"ERR_VEHICLE_MUST_HAVE_ZONE\", but response contained \"ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER\"\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:129:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:424:28)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Business Rule Validation Tests BR-25: New vehicle must have an initial zone assignment should reject vehicle creation without zone", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject vehicle creation without zone"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-26: Prevent cross-fleet resource access"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: Expected error_id \"ERR_FORBIDDEN\", but response contained \"ERR_VEHICLE_NOT_FOUND\"\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:129:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:447:28)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Business Rule Validation Tests BR-26: Prevent cross-fleet resource access should reject access to vehicle from different fleet", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject access to vehicle from different fleet"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-27: Reject invalid user role header"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-27: Reject invalid user role header should reject request with invalid user role", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject request with invalid user role"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-28: Only modify renter-specific fields on renters"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-28: Only modify renter-specific fields on renters should reject suspension attempt on non-renter user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject suspension attempt on non-renter user"}, {"ancestorTitles": ["Business Rule Validation Tests", "BR-29: Reject patch with invalid or immutable fields"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests BR-29: Reject patch with invalid or immutable fields should reject attempt to change user role", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "passed", "title": "should reject attempt to change user role"}, {"ancestorTitles": ["Business Rule Validation Tests", "ADV-1: Two renters attempt to rent the same vehicle concurrently"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: Expected status 201, got 500\n    at handleApiResponse (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:200:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:516:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Business Rule Validation Tests ADV-1: Two renters attempt to rent the same vehicle concurrently should allow both quotes but only first confirmation should succeed", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should allow both quotes but only first confirmation should succeed"}, {"ancestorTitles": ["Business Rule Validation Tests", "ADV-2: Hold placed on vehicle during rental flow triggers cancellation"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: Response envelope must be an object\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:96:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:557:28)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Business Rule Validation Tests ADV-2: Hold placed on vehicle during rental flow triggers cancellation should reject confirmation when maintenance hold is placed after quote", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject confirmation when maintenance hold is placed after quote"}, {"ancestorTitles": ["Business Rule Validation Tests", "ADV-3: Manual zone lock during rental prevents return to that zone"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:568:32)"], "fullName": "Business Rule Validation Tests ADV-3: Manual zone lock during rental prevents return to that zone should prevent return to zone that gets locked during rental", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should prevent return to zone that gets locked during rental"}, {"ancestorTitles": ["Business Rule Validation Tests", "ADV-6: Mixed infractions (damage + late) in one return are handled correctly"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "Business Rule Validation Tests ADV-6: Mixed infractions (damage + late) in one return are handled correctly should handle late return with damage without returning error", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle late return with damage without returning error"}, {"ancestorTitles": ["Business Rule Validation Tests", "Boundary Limits (LIMIT)", "LIMIT-1: Mileage ledger near service threshold"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: Unknown error_id \"ERR_INVALID_LAST_SERVICE_ODOMETER\" received. Not found in HTTP_STATUS_CODES map.\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:135:11)\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:164:7)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:624:39)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Business Rule Validation Tests Boundary Limits (LIMIT) LIMIT-1: Mileage ledger near service threshold should reject rental when vehicle is at service threshold", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should reject rental when vehicle is at service threshold"}, {"ancestorTitles": ["Business Rule Validation Tests", "Boundary Limits (LIMIT)", "LIMIT-4: Override distance exactly at limit"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.setupHighRepRenter (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:372:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:637:24)"], "fullName": "Business Rule Validation Tests Boundary Limits (LIMIT) LIMIT-4: Override distance exactly at limit should allow override rental when distance equals allowed miles", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should allow override rental when distance equals allowed miles"}, {"ancestorTitles": ["Business Rule Validation Tests", "Boundary Limits (LIMIT)", "LIMIT-5: Late return exactly on the boundary of quoted duration"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:652:28)"], "fullName": "Business Rule Validation Tests Boundary Limits (LIMIT) LIMIT-5: Late return exactly on the boundary of quoted duration should not be considered late when returned exactly at duration limit", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should not be considered late when returned exactly at duration limit"}, {"ancestorTitles": ["Business Rule Validation Tests", "Cross-Resource Effects (XRES)", "XRES-1: Rental completion updates Zone Balance Ledger"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:667:28)"], "fullName": "Business Rule Validation Tests Cross-Resource Effects (XRES) XRES-1: Rental completion updates Zone Balance Ledger should credit destination zone surplus by +1 on return", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should credit destination zone surplus by +1 on return"}, {"ancestorTitles": ["Business Rule Validation Tests", "Cross-Resource Effects (XRES)", "XRES-2: Rental start updates Zone Balance Ledger"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:705:38)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Business Rule Validation Tests Cross-Resource Effects (XRES) XRES-2: Rental start updates Zone Balance Ledger should debit start zone surplus by -1 on rental activation", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should debit start zone surplus by -1 on rental activation"}, {"ancestorTitles": ["Business Rule Validation Tests", "Cross-Resource Effects (XRES)", "XRES-3: Maintenance override rental affects Vehicle Mileage Ledger"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.setupHighRepRenter (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:372:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:727:24)"], "fullName": "Business Rule Validation Tests Cross-Resource Effects (XRES) XRES-3: Maintenance override rental affects Vehicle Mileage Ledger should subtract override credit from recorded miles on return", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should subtract override credit from recorded miles on return"}, {"ancestorTitles": ["Business Rule Validation Tests", "Cross-Resource Effects (XRES)", "XRES-4: Late return increments late_counter and reduces reputation"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:767:28)"], "fullName": "Business Rule Validation Tests Cross-Resource Effects (XRES) XRES-4: Late return increments late_counter and reduces reputation should update renter reputation ledger on late return", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should update renter reputation ledger on late return"}, {"ancestorTitles": ["Business Rule Validation Tests", "Cross-Resource Effects (XRES)", "XRES-5: Major damage infraction does not auto-suspend"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts:793:28)"], "fullName": "Business Rule Validation Tests Cross-Resource Effects (XRES) XRES-5: Major damage infraction does not auto-suspend should not auto-suspend for damage without late return trigger", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should not auto-suspend for damage without late return trigger"}], "endTime": 1751261096857, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-2: User with existing active rental cannot create a new quote › should reject quote creation by user with active rental\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:44:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-4: Vehicle not rentable due to state › should reject quote for IN_USE vehicle\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:74:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-6: Vehicle not rentable due to required maintenance › should reject quote for vehicle due for service\u001b[39m\u001b[22m\n\n    Unknown error_id \"ERR_INVALID_LAST_SERVICE_ODOMETER\" received. Not found in HTTP_STATUS_CODES map.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 133 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 134 |\u001b[39m   \u001b[36mif\u001b[39m (expectedHttpStatusFromMap \u001b[33m===\u001b[39m undefined) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 135 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Unknown error_id \"${actualErrorIdFromResponse}\" received. Not found in HTTP_STATUS_CODES map.`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 136 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 137 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 138 |\u001b[39m   \u001b[36mif\u001b[39m (actualHttpStatus \u001b[33m!==\u001b[39m expectedHttpStatusFromMap) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:135:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:164:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.createTestVehicle (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:214:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:105:34)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-7: Override rental blocked by insufficient rep › should reject override attempt with low reputation\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupLowRepRenter (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:391:41)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:123:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-8: Override rental blocked by multiple holds › should reject override attempt with multiple holds\u001b[39m\u001b[22m\n\n    Unknown error_id \"ERR_INVALID_LAST_SERVICE_ODOMETER\" received. Not found in HTTP_STATUS_CODES map.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 133 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 134 |\u001b[39m   \u001b[36mif\u001b[39m (expectedHttpStatusFromMap \u001b[33m===\u001b[39m undefined) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 135 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Unknown error_id \"${actualErrorIdFromResponse}\" received. Not found in HTTP_STATUS_CODES map.`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 136 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 137 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 138 |\u001b[39m   \u001b[36mif\u001b[39m (actualHttpStatus \u001b[33m!==\u001b[39m expectedHttpStatusFromMap) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:135:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:164:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.createTestVehicle (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:214:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:138:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-9: Override rental blocked by non-overridable hold › should reject override attempt with non-overridable hold\u001b[39m\u001b[22m\n\n    Unknown error_id \"ERR_INVALID_LAST_SERVICE_ODOMETER\" received. Not found in HTTP_STATUS_CODES map.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 133 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 134 |\u001b[39m   \u001b[36mif\u001b[39m (expectedHttpStatusFromMap \u001b[33m===\u001b[39m undefined) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 135 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Unknown error_id \"${actualErrorIdFromResponse}\" received. Not found in HTTP_STATUS_CODES map.`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 136 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 137 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 138 |\u001b[39m   \u001b[36mif\u001b[39m (actualHttpStatus \u001b[33m!==\u001b[39m expectedHttpStatusFromMap) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:135:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:164:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.createTestVehicle (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:214:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:171:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-10: Override quote rejected if distance estimate exceeds allowed miles › should reject override when distance exceeds allowed miles\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupHighRepRenter (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:372:41)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:197:29)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-14: Quote tampering detected on rental confirmation › should reject rental confirmation with tampered price\u001b[39m\u001b[22m\n\n    Response envelope must be an object\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 94 |\u001b[39m   \u001b[22m\n\u001b[2m     \u001b[90m 95 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39menvelope \u001b[33m||\u001b[39m \u001b[36mtypeof\u001b[39m envelope \u001b[33m!==\u001b[39m \u001b[32m'object'\u001b[39m) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 96 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'Response envelope must be an object'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 97 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 98 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 99 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39menvelope\u001b[33m.\u001b[39mmeta \u001b[33m||\u001b[39m \u001b[36mtypeof\u001b[39m envelope\u001b[33m.\u001b[39mmeta \u001b[33m!==\u001b[39m \u001b[32m'object'\u001b[39m) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:96:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:265:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-15: Prevent cancellation of non-confirmed rental › should reject cancellation of active rental\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:271:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-16: Prevent return on a rental that is not active › should reject return on confirmed but not active rental\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:295:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-17: Return fails if destination zone does not exist › should reject return to non-existent zone\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:306:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-18: Return fails if destination zone is HARD_LOCK › should reject return to HARD_LOCK zone\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:319:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-19: Reject return with odometer rollback › should reject return with end odometer < start odometer\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:332:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-25: New vehicle must have an initial zone assignment › should reject vehicle creation without zone\u001b[39m\u001b[22m\n\n    Expected error_id \"ERR_VEHICLE_MUST_HAVE_ZONE\", but response contained \"ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER\"\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 127 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 128 |\u001b[39m   \u001b[36mif\u001b[39m (options\u001b[33m.\u001b[39mexpectedErrorId \u001b[33m&&\u001b[39m actualErrorIdFromResponse \u001b[33m!==\u001b[39m options\u001b[33m.\u001b[39mexpectedErrorId) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 129 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Expected error_id \"${options.expectedErrorId}\", but response contained \"${actualErrorIdFromResponse}\"`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 130 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 131 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 132 |\u001b[39m   \u001b[36mconst\u001b[39m expectedHttpStatusFromMap \u001b[33m=\u001b[39m \u001b[33mHTTP_STATUS_CODES\u001b[39m[actualErrorIdFromResponse]\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:129:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:424:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › BR-26: Prevent cross-fleet resource access › should reject access to vehicle from different fleet\u001b[39m\u001b[22m\n\n    Expected error_id \"ERR_FORBIDDEN\", but response contained \"ERR_VEHICLE_NOT_FOUND\"\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 127 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 128 |\u001b[39m   \u001b[36mif\u001b[39m (options\u001b[33m.\u001b[39mexpectedErrorId \u001b[33m&&\u001b[39m actualErrorIdFromResponse \u001b[33m!==\u001b[39m options\u001b[33m.\u001b[39mexpectedErrorId) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 129 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Expected error_id \"${options.expectedErrorId}\", but response contained \"${actualErrorIdFromResponse}\"`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 130 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 131 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 132 |\u001b[39m   \u001b[36mconst\u001b[39m expectedHttpStatusFromMap \u001b[33m=\u001b[39m \u001b[33mHTTP_STATUS_CODES\u001b[39m[actualErrorIdFromResponse]\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:129:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:447:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › ADV-1: Two renters attempt to rent the same vehicle concurrently › should allow both quotes but only first confirmation should succeed\u001b[39m\u001b[22m\n\n    Expected status 201, got 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 198 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 199 |\u001b[39m     }\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 200 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 201 |\u001b[39m       \u001b[32m`Expected status ${statusArray.join(' or ')}, got ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 202 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 203 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat handleApiResponse (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:200:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:516:40)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › ADV-2: Hold placed on vehicle during rental flow triggers cancellation › should reject confirmation when maintenance hold is placed after quote\u001b[39m\u001b[22m\n\n    Response envelope must be an object\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 94 |\u001b[39m   \u001b[22m\n\u001b[2m     \u001b[90m 95 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39menvelope \u001b[33m||\u001b[39m \u001b[36mtypeof\u001b[39m envelope \u001b[33m!==\u001b[39m \u001b[32m'object'\u001b[39m) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 96 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'Response envelope must be an object'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 97 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 98 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 99 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39menvelope\u001b[33m.\u001b[39mmeta \u001b[33m||\u001b[39m \u001b[36mtypeof\u001b[39m envelope\u001b[33m.\u001b[39mmeta \u001b[33m!==\u001b[39m \u001b[32m'object'\u001b[39m) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:96:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:557:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › ADV-3: Manual zone lock during rental prevents return to that zone › should prevent return to zone that gets locked during rental\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:568:32)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › Boundary Limits (LIMIT) › LIMIT-1: Mileage ledger near service threshold › should reject rental when vehicle is at service threshold\u001b[39m\u001b[22m\n\n    Unknown error_id \"ERR_INVALID_LAST_SERVICE_ODOMETER\" received. Not found in HTTP_STATUS_CODES map.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 133 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 134 |\u001b[39m   \u001b[36mif\u001b[39m (expectedHttpStatusFromMap \u001b[33m===\u001b[39m undefined) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 135 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Unknown error_id \"${actualErrorIdFromResponse}\" received. Not found in HTTP_STATUS_CODES map.`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 136 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 137 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 138 |\u001b[39m   \u001b[36mif\u001b[39m (actualHttpStatus \u001b[33m!==\u001b[39m expectedHttpStatusFromMap) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:135:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:164:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:624:39)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › Boundary Limits (LIMIT) › LIMIT-4: Override distance exactly at limit › should allow override rental when distance equals allowed miles\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupHighRepRenter (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:372:41)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:637:24)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › Boundary Limits (LIMIT) › LIMIT-5: Late return exactly on the boundary of quoted duration › should not be considered late when returned exactly at duration limit\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:652:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › Cross-Resource Effects (XRES) › XRES-1: Rental completion updates Zone Balance Ledger › should credit destination zone surplus by +1 on return\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:667:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › Cross-Resource Effects (XRES) › XRES-2: Rental start updates Zone Balance Ledger › should debit start zone surplus by -1 on rental activation\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:705:38)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › Cross-Resource Effects (XRES) › XRES-3: Maintenance override rental affects Vehicle Mileage Ledger › should subtract override credit from recorded miles on return\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupHighRepRenter (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:372:41)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:727:24)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › Cross-Resource Effects (XRES) › XRES-4: Late return increments late_counter and reduces reputation › should update renter reputation ledger on late return\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:767:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mBusiness Rule Validation Tests › Cross-Resource Effects (XRES) › XRES-5: Major damage infraction does not auto-suspend › should not auto-suspend for damage without late return trigger\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/02.test.ts\u001b[39m\u001b[0m\u001b[2m:793:28)\u001b[22m\u001b[2m\u001b[22m\n", "name": "/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/02.test.ts", "startTime": 1751261096705, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Probationary Renter Tests (PROB-)"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:34:34)"], "fullName": "Car Share API Advanced Features Test Suite Probationary Renter Tests (PROB-) PROB-1: <PERSON><PERSON> enters PROBATIONARY status when reputation drops below 75", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "PROB-1: <PERSON><PERSON> enters PROBATIONARY status when reputation drops below 75"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Probationary Renter Tests (PROB-)"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: Expected response_type \"error\", got \"object\"\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:112:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:93:28)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Probationary Renter Tests (PROB-) PROB-2: PROBATIONARY renter is blocked from renting during HIGH_ALERT", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "PROB-2: PROBATIONARY renter is blocked from renting during HIGH_ALERT"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Probationary Renter Tests (PROB-)"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[1mMatcher error\u001b[22m: \u001b[31mreceived\u001b[39m value must be a number or bigint\n\nReceived has value: \u001b[31mundefined\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:128:42)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Probationary Renter Tests (PROB-) PROB-3: Non-probationary renter is NOT blocked from renting during HIGH_ALERT", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "PROB-3: Non-probationary renter is NOT blocked from renting during HIGH_ALERT"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Probationary Renter Tests (PROB-)"], "duration": 4, "failureDetails": [{"matcherResult": {"actual": 25, "expected": 14, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m14\u001b[39m\nReceived: \u001b[31m25\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m14\u001b[39m\nReceived: \u001b[31m25\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:153:44)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Probationary Renter Tests (PROB-) PROB-4: PROBATIONARY renter gets a stricter rental duration quote", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "PROB-4: PROBATIONARY renter gets a stricter rental duration quote"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Probationary Renter Tests (PROB-)"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: Expected response_type \"error\", got \"object\"\n    at validateErrorEnvelope (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:112:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:177:28)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Probationary Renter Tests (PROB-) PREC-7: Hierarchy: User suspension is checked before probationary restriction", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "PREC-7: Hierarchy: User suspension is checked before probationary restriction"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Probationary Renter Tests (PROB-)"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:209:36)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Probationary Renter Tests (PROB-) E2E-6: Probationary renter's stricter duration causes a late return, deepening probation", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "E2E-6: Probationary renter's stricter duration causes a late return, deepening probation"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Zone-Based Incentives Tests (ZINC-)"], "duration": 6, "failureDetails": [{"matcherResult": {"expected": null, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mNaN\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mNaN\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:284:43)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Zone-Based Incentives Tests (ZINC-) ZINC-1: Quoting a discouraged vehicle class from a zone applies a 5% surcharge", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "ZINC-1: Quoting a discouraged vehicle class from a zone applies a 5% surcharge"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Zone-Based Incentives Tests (ZINC-)"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_LUXURY_LOCK - Insufficient reputation for luxury vehicles\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:316:35)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Zone-Based Incentives Tests (ZINC-) ZINC-2: Returning an incentivized vehicle class to a zone grants a 5-point reputation bonus", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "ZINC-2: Returning an incentivized vehicle class to a zone grants a 5-point reputation bonus"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Zone-Based Incentives Tests (ZINC-)"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_LUXURY_LOCK - Insufficient reputation for luxury vehicles\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:375:35)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Zone-Based Incentives Tests (ZINC-) ZINC-3: Returning a discouraged vehicle class to a zone applies a 5-point reputation penalty", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "ZINC-3: Returning a discouraged vehicle class to a zone applies a 5-point reputation penalty"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Zone-Based Incentives Tests (ZINC-)"], "duration": 5, "failureDetails": [{"matcherResult": {"expected": null, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mNaN\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mNaN\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:453:33)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Zone-Based Incentives Tests (ZINC-) ZINC-4: Cumulative Surcharges: HIGH_ALERT and Zone Discouragement are applied together", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "ZINC-4: Cumulative Surcharges: HIGH_ALERT and Zone Discouragement are applied together"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Zone-Based Incentives Tests (ZINC-)"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:474:48)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Zone-Based Incentives Tests (ZINC-) E2E-7: <PERSON><PERSON> escapes probationary status by using a zone incentive", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "E2E-7: <PERSON><PERSON> escapes probationary status by using a zone incentive"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Zone-Based Incentives Tests (ZINC-)"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:612:37)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Zone-Based Incentives Tests (ZINC-) XRES-7: Late return penalty and zone incentive bonus are both applied", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "XRES-7: Late return penalty and zone incentive bonus are both applied"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Fleet Phase State Tests (PHASE-)"], "duration": 7, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:699:43)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Fleet Phase State Tests (PHASE-) PHASE-1: Fleet enters HIGH_ALERT when 3 zones reach HARD_LOCK", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "PHASE-1: Fleet enters HIGH_ALERT when 3 zones reach HARD_LOCK"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Fleet Phase State Tests (PHASE-)"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Car Share API Advanced Features Test Suite Fleet Phase State Tests (PHASE-) PHASE-2: Fleet returns to NORMAL when HARD_LOCK zones drop to 2", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "PHASE-2: Fleet returns to NORMAL when HARD_LOCK zones drop to 2"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Fleet Action Counter Tests (FAC-)"], "duration": 3, "failureDetails": [{"matcherResult": {"expected": null, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mNaN\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mNaN\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:748:26)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Fleet Action Counter Tests (FAC-) FAC-1: POST endpoints increment Fleet Action Counter", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "FAC-1: POST endpoints increment Fleet Action Counter"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Fleet Action Counter Tests (FAC-)"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER - Valid X-User-ID and X-User-Role headers are required\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:762:35)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Car Share API Advanced Features Test Suite Fleet Action Counter Tests (FAC-) FAC-2: PATCH endpoints increment Fleet Action Counter", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "FAC-2: PATCH endpoints increment Fleet Action Counter"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Fleet Action Counter Tests (FAC-)"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Car Share API Advanced Features Test Suite Fleet Action Counter Tests (FAC-) FAC-3: GET endpoints do NOT increment Fleet Action Counter", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "FAC-3: GET endpoints do NOT increment Fleet Action Counter"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Fleet Action Counter Tests (FAC-)"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Car Share API Advanced Features Test Suite Fleet Action Counter Tests (FAC-) FAC-4: Failed state-modifying requests do NOT increment counter", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "FAC-4: Failed state-modifying requests do NOT increment counter"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Condition Reports Tests (CR-)"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Car Share API Advanced Features Test Suite Condition Reports Tests (CR-) CR-1: SPOT report with MINOR damage on available vehicle", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "CR-1: SPOT report with MINOR damage on available vehicle"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Condition Reports Tests (CR-)"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Car Share API Advanced Features Test Suite Condition Reports Tests (CR-) CR-2: MAINT_CHECK report with MAJOR damage transitions vehicle", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "CR-2: MAINT_CHECK report with MAJOR damage transitions vehicle"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Condition Reports Tests (CR-)"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts:908:43)"], "fullName": "Car Share API Advanced Features Test Suite Condition Reports Tests (CR-) CR-3: SPOT report on RESERVED vehicle with MAJOR damage cancels rental", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "CR-3: SPOT report on RESERVED vehicle with MAJOR damage cancels rental"}, {"ancestorTitles": ["Car Share API Advanced Features Test Suite", "Battery Charging Tests (BATT-)"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Car Share API Advanced Features Test Suite Battery Charging Tests (BATT-) BATT-1: Successful EV charging sets battery to 100%", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "BATT-1: Successful EV charging sets battery to 100%"}], "endTime": 1751261096982, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Probationary Renter Tests (PROB-) › PROB-1: <PERSON><PERSON> enters PROBATIONARY status when reputation drops below 75\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:34:34)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Probationary Renter Tests (PROB-) › PROB-2: PROBATIONARY renter is blocked from renting during HIGH_ALERT\u001b[39m\u001b[22m\n\n    Expected response_type \"error\", got \"object\"\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 110 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 111 |\u001b[39m   \u001b[36mif\u001b[39m (envelope\u001b[33m.\u001b[39mresponse_type \u001b[33m!==\u001b[39m \u001b[32m'error'\u001b[39m) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 112 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Expected response_type \"error\", got \"${envelope.response_type}\"`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 113 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 114 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 115 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39menvelope\u001b[33m.\u001b[39mdata \u001b[33m||\u001b[39m \u001b[36mtypeof\u001b[39m envelope\u001b[33m.\u001b[39mdata \u001b[33m!==\u001b[39m \u001b[32m'object'\u001b[39m \u001b[33m||\u001b[39m \u001b[33mArray\u001b[39m\u001b[33m.\u001b[39misArray(envelope\u001b[33m.\u001b[39mdata)) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:112:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:93:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Probationary Renter Tests (PROB-) › PROB-3: Non-probationary renter is NOT blocked from renting during HIGH_ALERT\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    \u001b[1mMatcher error\u001b[22m: \u001b[31mreceived\u001b[39m value must be a number or bigint\n\n    Received has value: \u001b[31mundefined\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 126 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 127 |\u001b[39m       \u001b[36mconst\u001b[39m highAlertQuote \u001b[33m=\u001b[39m getSuccessData(highAlertQuoteResponse)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 128 |\u001b[39m       expect(highAlertQuote\u001b[33m.\u001b[39mprice_cents)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 129 |\u001b[39m       \u001b[36mconst\u001b[39m expectedSurchargedPrice \u001b[33m=\u001b[39m \u001b[33mMath\u001b[39m\u001b[33m.\u001b[39mround(basePrice \u001b[33m*\u001b[39m \u001b[35m1.15\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 130 |\u001b[39m       expect(highAlertQuote\u001b[33m.\u001b[39mprice_cents)\u001b[33m.\u001b[39mtoBe(expectedSurchargedPrice)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 131 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:128:42)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Probationary Renter Tests (PROB-) › PROB-4: PROBATIONARY renter gets a stricter rental duration quote\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m14\u001b[39m\n    Received: \u001b[31m25\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 151 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 152 |\u001b[39m       \u001b[36mconst\u001b[39m quote \u001b[33m=\u001b[39m getSuccessData(quoteResponse)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 153 |\u001b[39m       expect(quote\u001b[33m.\u001b[39mquoted_action_duration)\u001b[33m.\u001b[39mtoBe(\u001b[35m14\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 154 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 155 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 156 |\u001b[39m     it(\u001b[32m'PREC-7: Hierarchy: User suspension is checked before probationary restriction'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:153:44)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Probationary Renter Tests (PROB-) › PREC-7: Hierarchy: User suspension is checked before probationary restriction\u001b[39m\u001b[22m\n\n    Expected response_type \"error\", got \"object\"\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 110 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 111 |\u001b[39m   \u001b[36mif\u001b[39m (envelope\u001b[33m.\u001b[39mresponse_type \u001b[33m!==\u001b[39m \u001b[32m'error'\u001b[39m) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 112 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`Expected response_type \"error\", got \"${envelope.response_type}\"`\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 113 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 114 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 115 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39menvelope\u001b[33m.\u001b[39mdata \u001b[33m||\u001b[39m \u001b[36mtypeof\u001b[39m envelope\u001b[33m.\u001b[39mdata \u001b[33m!==\u001b[39m \u001b[32m'object'\u001b[39m \u001b[33m||\u001b[39m \u001b[33mArray\u001b[39m\u001b[33m.\u001b[39misArray(envelope\u001b[33m.\u001b[39mdata)) {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat validateErrorEnvelope (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:112:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:177:28)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Probationary Renter Tests (PROB-) › E2E-6: Probationary renter's stricter duration causes a late return, deepening probation\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:209:36)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Zone-Based Incentives Tests (ZINC-) › ZINC-1: Quoting a discouraged vehicle class from a zone applies a 5% surcharge\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32mNaN\u001b[39m\n    Received: \u001b[31mundefined\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 282 |\u001b[39m       \u001b[36mconst\u001b[39m discourageQuote \u001b[33m=\u001b[39m getSuccessData(discourageQuoteResponse)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 283 |\u001b[39m       \u001b[36mconst\u001b[39m expectedSurchargedPrice \u001b[33m=\u001b[39m \u001b[33mMath\u001b[39m\u001b[33m.\u001b[39mround(basePrice \u001b[33m*\u001b[39m \u001b[35m1.05\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 284 |\u001b[39m       expect(discourageQuote\u001b[33m.\u001b[39mprice_cents)\u001b[33m.\u001b[39mtoBe(expectedSurchargedPrice)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 285 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 286 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 287 |\u001b[39m     it(\u001b[32m'ZINC-2: Returning an incentivized vehicle class to a zone grants a 5-point reputation bonus'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:284:43)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Zone-Based Incentives Tests (ZINC-) › ZINC-2: Returning an incentivized vehicle class to a zone grants a 5-point reputation bonus\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_LUXURY_LOCK - Insufficient reputation for luxury vehicles\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:316:35)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Zone-Based Incentives Tests (ZINC-) › ZINC-3: Returning a discouraged vehicle class to a zone applies a 5-point reputation penalty\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_LUXURY_LOCK - Insufficient reputation for luxury vehicles\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:375:35)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Zone-Based Incentives Tests (ZINC-) › ZINC-4: Cumulative Surcharges: HIGH_ALERT and Zone Discouragement are applied together\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32mNaN\u001b[39m\n    Received: \u001b[31mundefined\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 451 |\u001b[39m       \u001b[36mconst\u001b[39m quote \u001b[33m=\u001b[39m getSuccessData(quoteResponse)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 452 |\u001b[39m       \u001b[36mconst\u001b[39m expectedSurchargedPrice \u001b[33m=\u001b[39m \u001b[33mMath\u001b[39m\u001b[33m.\u001b[39mround(basePrice \u001b[33m*\u001b[39m \u001b[35m1.20\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 453 |\u001b[39m       expect(quote\u001b[33m.\u001b[39mprice_cents)\u001b[33m.\u001b[39mtoBe(expectedSurchargedPrice)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 454 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 455 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 456 |\u001b[39m     it(\u001b[32m'E2E-7: Renter escapes probationary status by using a zone incentive'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:453:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Zone-Based Incentives Tests (ZINC-) › E2E-7: Renter escapes probationary status by using a zone incentive\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:474:48)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Zone-Based Incentives Tests (ZINC-) › XRES-7: Late return penalty and zone incentive bonus are both applied\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:612:37)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Fleet Phase State Tests (PHASE-) › PHASE-1: Fleet enters HIGH_ALERT when 3 zones reach HARD_LOCK\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:699:43)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Fleet Action Counter Tests (FAC-) › FAC-1: POST endpoints increment Fleet Action Counter\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32mNaN\u001b[39m\n    Received: \u001b[31mundefined\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 746 |\u001b[39m       \u001b[36mconst\u001b[39m finalCount \u001b[33m=\u001b[39m finalFleet\u001b[33m.\u001b[39maction_count\u001b[33m!\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 747 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 748 |\u001b[39m       expect(finalCount)\u001b[33m.\u001b[39mtoBe(initialCount \u001b[33m+\u001b[39m \u001b[35m2\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 749 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 750 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 751 |\u001b[39m     it(\u001b[32m'FAC-2: PATCH endpoints increment Fleet Action Counter'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:748:26)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Fleet Action Counter Tests (FAC-) › FAC-2: PATCH endpoints increment Fleet Action Counter\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER - Valid X-User-ID and X-User-Role headers are required\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:762:35)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCar Share API Advanced Features Test Suite › Condition Reports Tests (CR-) › CR-3: SPOT report on RESERVED vehicle with MAJOR damage cancels rental\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/03.test.ts\u001b[39m\u001b[0m\u001b[2m:908:43)\u001b[22m\u001b[2m\u001b[22m\n", "name": "/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/03.test.ts", "startTime": 1751261096860, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-1: Vehicle AVAILABLE -> RESERVED on rental confirmation"], "duration": 5, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:46:38)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Vehicle State Machine VSM-1: Vehicle AVAILABLE -> RESERVED on rental confirmation should transition vehicle to RESERVED and create CONFIRMED rental", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should transition vehicle to RESERVED and create CONFIRMED rental"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-2: Vehicle RESERVED -> IN_USE on renter unlock"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:62:23)"], "fullName": "State Machine Tests Vehicle State Machine VSM-2: Vehicle RESERVED -> IN_USE on renter unlock should transition vehicle to IN_USE and rental to ACTIVE", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should transition vehicle to IN_USE and rental to ACTIVE"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-3: Vehicle RESERVED -> AVAILABLE on rental cancellation"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:93:38)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Vehicle State Machine VSM-3: Vehicle RESERVED -> AVAILABLE on rental cancellation should return vehicle to AVAILABLE and cancel rental", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return vehicle to AVAILABLE and cancel rental"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-5: Vehicle IN_USE -> AVAILABLE on clean return"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:113:23)"], "fullName": "State Machine Tests Vehicle State Machine VSM-5: Vehicle IN_USE -> AVAILABLE on clean return should complete rental and return vehicle to AVAILABLE", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should complete rental and return vehicle to AVAILABLE"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-6: Vehicle IN_USE -> NEEDS_CLEANING on poor cleanliness return"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:137:23)"], "fullName": "State Machine Tests Vehicle State Machine VSM-6: Vehicle IN_USE -> NEEDS_CLEANING on poor cleanliness return should transition vehicle to NEEDS_CLEANING with CLEAN_REQ flag", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should transition vehicle to NEEDS_CLEANING with CLEAN_REQ flag"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-7: Vehicle IN_USE -> NEEDS_MAINTENANCE on major damage or service due"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:158:23)"], "fullName": "State Machine Tests Vehicle State Machine VSM-7: Vehicle IN_USE -> NEEDS_MAINTENANCE on major damage or service due should transition to NEEDS_MAINTENANCE on major damage", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should transition to NEEDS_MAINTENANCE on major damage"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-9: Vehicle NEEDS_CLEANING -> AVAILABLE after cleaning"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_VEHICLE_NOT_DIRTY - Vehicle is not in needs cleaning state\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:192:46)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Vehicle State Machine VSM-9: Vehicle NEEDS_CLEANING -> AVAILABLE after cleaning should transition to AVAIL<PERSON>LE after cleaning completion", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should transition to AVAILABLE after cleaning completion"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-10: Vehicle NEEDS_CLEANING -> NEEDS_MAINTENANCE if maintenance still due"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_VEHICLE_NOT_DIRTY - Vehicle is not in needs cleaning state\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:215:46)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Vehicle State Machine VSM-10: Vehicle NEEDS_CLEANING -> NEEDS_MAINTENANCE if maintenance still due should transition to NEEDS_MAINTENANCE if maintenance needed after cleaning", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should transition to NEEDS_MAINTENANCE if maintenance needed after cleaning"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-11: Vehicle NEEDS_MAINTENANCE -> AVAILABLE after service"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "State Machine Tests Vehicle State Machine VSM-11: Vehicle NEEDS_MAINTENANCE -> AVAIL<PERSON>LE after service should transition to AVAIL<PERSON>LE after successful service", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should transition to AVAILABLE after successful service"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-12: Vehicle NEEDS_MAINTENANCE -> NEEDS_CLEANING if dirty after service"], "duration": 3, "failureDetails": [{"matcherResult": {"actual": "AVAILABLE", "expected": "NEEDS_CLEANING", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"NEEDS_CLEANING\"\u001b[39m\nReceived: \u001b[31m\"AVAILABLE\"\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"NEEDS_CLEANING\"\u001b[39m\nReceived: \u001b[31m\"AVAILABLE\"\u001b[39m\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:263:39)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Vehicle State Machine VSM-12: Vehicle NEEDS_MAINTENANCE -> NEEDS_CLEANING if dirty after service should transition to NEEDS_CLEANING if CLEAN_REQ flag remains after service", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should transition to NEEDS_CLEANING if CLEAN_REQ flag remains after service"}, {"ancestorTitles": ["State Machine Tests", "Vehicle State Machine", "VSM-13: ANY state -> RETIRED on decommission"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:166:13)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:277:46)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Vehicle State Machine VSM-13: ANY state -> RETIRED on decommission should permanently retire vehicle from any state", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should permanently retire vehicle from any state"}, {"ancestorTitles": ["State Machine Tests", "Rental State Machine", "RSM-1: Rental CONFIRMED -> ACTIVE on renter unlock"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:300:38)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Rental State Machine RSM-1: Rental CONFIRMED -> ACTIVE on renter unlock should activate rental on unlock", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should activate rental on unlock"}, {"ancestorTitles": ["State Machine Tests", "Rental State Machine", "RSM-4: Rental ACTIVE -> COMPLETED on normal return"], "duration": 10, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:311:23)"], "fullName": "State Machine Tests Rental State Machine RSM-4: Rental ACTIVE -> COMPLETED on normal return should complete rental on successful return", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should complete rental on successful return"}, {"ancestorTitles": ["State Machine Tests", "Rental State Machine", "RSM-5: Rental ACTIVE -> INFRACTION_REVIEW on damage return"], "duration": 2, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:331:23)"], "fullName": "State Machine Tests Rental State Machine RSM-5: Rental ACTIVE -> INFRACTION_REVIEW on damage return should flag rental for review on damage return", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should flag rental for review on damage return"}, {"ancestorTitles": ["State Machine Tests", "Rental State Machine", "RSM-6: Rental INFRACTION_REVIEW -> COMPLETED on operator resolution"], "duration": 3, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.setupActiveRentalScenario (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/fixtures.ts:334:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:351:23)"], "fullName": "State Machine Tests Rental State Machine RSM-6: Rental INFRACTION_REVIEW -> COMPLETED on operator resolution should complete rental and release renter after operator review", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should complete rental and release renter after operator review"}, {"ancestorTitles": ["State Machine Tests", "Zone State Machine", "ZSM-1: Zone OPEN -> SOFT_LOCK when surplus drops to -5"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:401:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Zone State Machine ZSM-1: Zone OPEN -> SOFT_LOCK when surplus drops to -5 should transition to SOFT_LOCK when surplus reaches -5", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should transition to SOFT_LOCK when surplus reaches -5"}, {"ancestorTitles": ["State Machine Tests", "Zone State Machine", "ZSM-2: Zone SOFT_LOCK -> HARD_LOCK when surplus drops to -10"], "duration": 6, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:468:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Zone State Machine ZSM-2: Zone SOFT_LOCK -> HARD_LOCK when surplus drops to -10 should transition to HARD_LOCK when surplus reaches -10", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should transition to HARD_LOCK when surplus reaches -10"}, {"ancestorTitles": ["State Machine Tests", "Zone State Machine", "ZSM-3: Zone SOFT_LOCK -> OPEN when surplus recovers above -5"], "duration": 4, "failureDetails": [{}], "failureMessages": ["Error: API call in test setup failed unexpectedly with status 500\n    at getSuccessData (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/helpers/validators.ts:171:11)\n    at Object.<anonymous> (/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts:536:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "State Machine Tests Zone State Machine ZSM-3: Zone SOFT_LOCK -> OPEN when surplus recovers above -5 should automatically return to OPEN when surplus improves", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should automatically return to OPEN when surplus improves"}, {"ancestorTitles": ["State Machine Tests", "Zone State Machine", "ZSM-4: Zone HARD_LOCK -> OPEN requires manual operator override"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "State Machine Tests Zone State Machine ZSM-4: Zone HARD_LOCK -> OPEN requires manual operator override should require two-step manual override to unlock hard-locked zone", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should require two-step manual override to unlock hard-locked zone"}], "endTime": 1751261097087, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-1: Vehicle AVAILABLE -> RESERVED on rental confirmation › should transition vehicle to RESERVED and create CONFIRMED rental\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:46:38)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-2: Vehicle RESERVED -> IN_USE on renter unlock › should transition vehicle to IN_USE and rental to ACTIVE\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:62:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-3: Vehicle RESERVED -> AVAILABLE on rental cancellation › should return vehicle to AVAILABLE and cancel rental\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:93:38)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-5: Vehicle IN_USE -> AVAILABLE on clean return › should complete rental and return vehicle to AVAILABLE\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:113:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-6: Vehicle IN_USE -> NEEDS_CLEANING on poor cleanliness return › should transition vehicle to NEEDS_CLEANING with CLEAN_REQ flag\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:137:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-7: Vehicle IN_USE -> NEEDS_MAINTENANCE on major damage or service due › should transition to NEEDS_MAINTENANCE on major damage\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:158:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-9: Vehicle NEEDS_CLEANING -> AVAILABLE after cleaning › should transition to AVAILABLE after cleaning completion\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_VEHICLE_NOT_DIRTY - Vehicle is not in needs cleaning state\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:192:46)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-10: Vehicle NEEDS_CLEANING -> NEEDS_MAINTENANCE if maintenance still due › should transition to NEEDS_MAINTENANCE if maintenance needed after cleaning\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_VEHICLE_NOT_DIRTY - Vehicle is not in needs cleaning state\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:215:46)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-12: Vehicle NEEDS_MAINTENANCE -> NEEDS_CLEANING if dirty after service › should transition to NEEDS_CLEANING if CLEAN_REQ flag remains after service\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"NEEDS_CLEANING\"\u001b[39m\n    Received: \u001b[31m\"AVAILABLE\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 261 |\u001b[39m         \u001b[36mconst\u001b[39m servicedVehicle \u001b[33m=\u001b[39m getSuccessData(vehicleResponse)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 262 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 263 |\u001b[39m         expect(servicedVehicle\u001b[33m.\u001b[39mstate)\u001b[33m.\u001b[39mtoBe(\u001b[33mVehicleState\u001b[39m\u001b[33m.\u001b[39m\u001b[33mNEEDS_CLEANING\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 264 |\u001b[39m         expect(servicedVehicle\u001b[33m.\u001b[39mflags)\u001b[33m.\u001b[39mtoContain(\u001b[33mVehicleFlag\u001b[39m\u001b[33m.\u001b[39m\u001b[33mCLEAN_REQ\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 265 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 266 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:263:39)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Vehicle State Machine › VSM-13: ANY state -> RETIRED on decommission › should permanently retire vehicle from any state\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with error: ERR_FORBIDDEN - User not in fleet\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 164 |\u001b[39m       validateErrorEnvelope(response\u001b[33m,\u001b[39m {})\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[36mconst\u001b[39m errorData \u001b[33m=\u001b[39m response\u001b[33m.\u001b[39mdata\u001b[33m.\u001b[39mdata \u001b[36mas\u001b[39m \u001b[33mErrorResponseData\u001b[39m\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 166 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m         \u001b[32m`API call in test setup failed unexpectedly with error: ${errorData.error_id} - ${errorData.message}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 168 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 169 |\u001b[39m     }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:166:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:277:46)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Rental State Machine › RSM-1: Rental CONFIRMED -> ACTIVE on renter unlock › should activate rental on unlock\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:300:38)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Rental State Machine › RSM-4: Rental ACTIVE -> COMPLETED on normal return › should complete rental on successful return\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:311:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Rental State Machine › RSM-5: Rental ACTIVE -> INFRACTION_REVIEW on damage return › should flag rental for review on damage return\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:331:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Rental State Machine › RSM-6: Rental INFRACTION_REVIEW -> COMPLETED on operator resolution › should complete rental and release renter after operator review\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.setupActiveRentalScenario (\u001b[22m\u001b[2mtest_suite/helpers/fixtures.ts\u001b[2m:334:34)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat async Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:351:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Zone State Machine › ZSM-1: Zone OPEN -> SOFT_LOCK when surplus drops to -5 › should transition to SOFT_LOCK when surplus reaches -5\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:401:40)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Zone State Machine › ZSM-2: Zone SOFT_LOCK -> HARD_LOCK when surplus drops to -10 › should transition to HARD_LOCK when surplus reaches -10\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:468:40)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mState Machine Tests › Zone State Machine › ZSM-3: Zone SOFT_LOCK -> OPEN when surplus recovers above -5 › should automatically return to OPEN when surplus improves\u001b[39m\u001b[22m\n\n    API call in test setup failed unexpectedly with status 500\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 169 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 170 |\u001b[39m     \u001b[90m// Otherwise, throw a generic error for the unexpected HTTP status.\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 171 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 172 |\u001b[39m       \u001b[32m`API call in test setup failed unexpectedly with status ${response.status}`\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 173 |\u001b[39m     )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 174 |\u001b[39m   }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat getSuccessData (\u001b[22m\u001b[2mtest_suite/helpers/validators.ts\u001b[2m:171:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest_suite/01.test.ts\u001b[39m\u001b[0m\u001b[2m:536:40)\u001b[22m\u001b[2m\u001b[22m\n", "name": "/Users/<USER>/Downloads/Mercor/prd-gen-instructions/car-share/car-share/test_suite/01.test.ts", "startTime": 1751261096985, "status": "failed", "summary": ""}], "wasInterrupted": false}