import { apiClient } from './helpers/api-client';
import { getSuccessData } from './helpers/validators';
import { buildUserHeaders } from './helpers/header-utils';
import { fixtures, factories } from './helpers/fixtures';
import {
  TestContext,
  VehicleState,
  RentalState,
  ZoneLockState,
  UserRole, CleanlinessGrade,
  DamageLevel,
  ReportType, VehicleFlag,
  ReviewRentalPayload
} from './helpers/types';

describe('State Machine Tests', () => {
  let testContext: TestContext;

  beforeEach(async () => {
    testContext = {
      fleetId: '',
      captures: {}
    };

    const fleet = await fixtures.createTestFleet(testContext);
    testContext.fleetId = fleet.id;
  });

  describe('Vehicle State Machine', () => {
    describe('VSM-1: Vehicle AVAILABLE -> RESERVED on rental confirmation', () => {
      it('should transition vehicle to RESERVED and create CONFIRMED rental', async () => {
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
        const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
        testContext.captures.renterId = renter.id;

        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
        const quote = getSuccessData(quoteResponse);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
        const rental = getSuccessData(rentalResponse);

        const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, vehicle.id, renterHeaders);
        const updatedVehicle = getSuccessData(vehicleResponse);

        const userResponse = await apiClient.getUser(testContext.fleetId, renter.id, renterHeaders);
        const updatedUser = getSuccessData(userResponse);

        expect(updatedVehicle.state).toBe(VehicleState.RESERVED);
        expect(rental.state).toBe(RentalState.CONFIRMED);
        expect(updatedUser.active_rental_id).toBe(rental.id);
      });
    });

    describe('VSM-2: Vehicle RESERVED -> IN_USE on renter unlock', () => {
      it('should transition vehicle to IN_USE and rental to ACTIVE', async () => {
        const setup = await fixtures.setupActiveRentalScenario(testContext);

        const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, setup.vehicle.id,
          buildUserHeaders(setup.renter.id, UserRole.RENTER));
        const vehicle = getSuccessData(vehicleResponse);

        const rentalResponse = await apiClient.getRental(testContext.fleetId, setup.rental.id,
          buildUserHeaders(setup.renter.id, UserRole.RENTER));
        const rental = getSuccessData(rentalResponse);

        expect(vehicle.state).toBe(VehicleState.IN_USE);
        expect(rental.state).toBe(RentalState.ACTIVE);
        expect(vehicle.current_zone_id).toBeNull();
      });
    });

    describe('VSM-3: Vehicle RESERVED -> AVAILABLE on rental cancellation', () => {
      it('should return vehicle to AVAILABLE and cancel rental', async () => {
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
        const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
        const quote = getSuccessData(quoteResponse);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
        const rental = getSuccessData(rentalResponse);

        await apiClient.cancelRental(testContext.fleetId, rental.id, renterHeaders);

        const cancelledRentalResponse = await apiClient.getRental(testContext.fleetId, rental.id, renterHeaders);
        const cancelledRental = getSuccessData(cancelledRentalResponse);

        const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, vehicle.id, renterHeaders);
        const updatedVehicle = getSuccessData(vehicleResponse);

        const userResponse = await apiClient.getUser(testContext.fleetId, renter.id, renterHeaders);
        const updatedUser = getSuccessData(userResponse);

        expect(updatedVehicle.state).toBe(VehicleState.AVAILABLE);
        expect(cancelledRental.state).toBe(RentalState.CANCELLED);
        expect(updatedUser.active_rental_id).toBeNull();
      });
    });
    describe('VSM-5: Vehicle IN_USE -> AVAILABLE on clean return', () => {
      it('should complete rental and return vehicle to AVAILABLE', async () => {
        const setup = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(setup.renter.id, UserRole.RENTER);

        const returnPayload = factories.returnRentalPayload(setup.zone.id, setup.vehicle.odometer_mi + 10, {
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.NONE
        });
        const returnResponse = await apiClient.returnRental(testContext.fleetId, setup.rental.id, returnPayload, renterHeaders);
        const returnedRental = getSuccessData(returnResponse);

        const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, setup.vehicle.id, renterHeaders);
        const updatedVehicle = getSuccessData(vehicleResponse);

        const userResponse = await apiClient.getUser(testContext.fleetId, setup.renter.id, renterHeaders);
        const updatedUser = getSuccessData(userResponse);

        expect(updatedVehicle.state).toBe(VehicleState.AVAILABLE);
        expect(returnedRental.state).toBe(RentalState.COMPLETED);
        expect(updatedUser.active_rental_id).toBeNull();
      });
    });

    describe('VSM-6: Vehicle IN_USE -> NEEDS_CLEANING on poor cleanliness return', () => {
      it('should transition vehicle to NEEDS_CLEANING with CLEAN_REQ flag', async () => {
        const setup = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(setup.renter.id, UserRole.RENTER);

        const returnPayload = factories.returnRentalPayload(setup.zone.id, setup.vehicle.odometer_mi + 10, {
          cleanliness_grade: CleanlinessGrade.POOR,
          damage_grade: DamageLevel.NONE
        });
        const returnResponse = await apiClient.returnRental(testContext.fleetId, setup.rental.id, returnPayload, renterHeaders);
        const returnedRental = getSuccessData(returnResponse);

        const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, setup.vehicle.id, renterHeaders);
        const updatedVehicle = getSuccessData(vehicleResponse);

        expect(updatedVehicle.state).toBe(VehicleState.NEEDS_CLEANING);
        expect(updatedVehicle.flags).toContain(VehicleFlag.CLEAN_REQ);
        expect(returnedRental.state).toBe(RentalState.COMPLETED);
      });
    });

    describe('VSM-7: Vehicle IN_USE -> NEEDS_MAINTENANCE on major damage or service due', () => {
      it('should transition to NEEDS_MAINTENANCE on major damage', async () => {
        const setup = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(setup.renter.id, UserRole.RENTER);

        const returnPayload = factories.returnRentalPayload(setup.zone.id, setup.vehicle.odometer_mi + 10, {
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.MAJOR
        });
        const returnResponse = await apiClient.returnRental(testContext.fleetId, setup.rental.id, returnPayload, renterHeaders);
        const returnedRental = getSuccessData(returnResponse);

        const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, setup.vehicle.id, renterHeaders);
        const updatedVehicle = getSuccessData(vehicleResponse);

        expect(updatedVehicle.state).toBe(VehicleState.NEEDS_MAINTENANCE);
        expect(returnedRental.state).toBe(RentalState.INFRACTION_REVIEW);
      });
    });

    describe('VSM-9: Vehicle NEEDS_CLEANING -> AVAILABLE after cleaning', () => {
      it('should transition to AVAILABLE after cleaning completion', async () => {
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const reportPayload = {
          vehicle_id: vehicle.id,
          report_type: ReportType.SPOT,
          cleanliness_grade: CleanlinessGrade.POOR,
          damage_grade: DamageLevel.NONE,
          mileage_recorded: vehicle.odometer_mi
        };
        await apiClient.createConditionReport(testContext.fleetId, reportPayload, operatorHeaders);

        const cleanPayload = { action: 'clean_complete' as const };
        const cleanResponse = await apiClient.updateVehicleServiceStatus(testContext.fleetId, vehicle.id, cleanPayload, operatorHeaders);
        const cleanedVehicle = getSuccessData(cleanResponse);

        expect(cleanedVehicle.state).toBe(VehicleState.AVAILABLE);
        expect(cleanedVehicle.flags).not.toContain(VehicleFlag.CLEAN_REQ);
      });
    });

    describe('VSM-10: Vehicle NEEDS_CLEANING -> NEEDS_MAINTENANCE if maintenance still due', () => {
      it('should transition to NEEDS_MAINTENANCE if maintenance needed after cleaning', async () => {
        const { zone, vehicle } = await fixtures.setupMaintenanceOverrideVehicle(testContext);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const reportPayload = {
          vehicle_id: vehicle.id,
          report_type: ReportType.SPOT,
          cleanliness_grade: CleanlinessGrade.POOR,
          damage_grade: DamageLevel.NONE,
          mileage_recorded: vehicle.odometer_mi
        };
        await apiClient.createConditionReport(testContext.fleetId, reportPayload, operatorHeaders);

        const cleanPayload = { action: 'clean_complete' as const };
        const cleanResponse = await apiClient.updateVehicleServiceStatus(testContext.fleetId, vehicle.id, cleanPayload, operatorHeaders);
        const cleanedVehicle = getSuccessData(cleanResponse);

        expect(cleanedVehicle.state).toBe(VehicleState.NEEDS_MAINTENANCE);
      });
    });

    describe('VSM-11: Vehicle NEEDS_MAINTENANCE -> AVAILABLE after service', () => {
      it('should transition to AVAILABLE after successful service', async () => {
        const { zone, vehicle } = await fixtures.setupMaintenanceOverrideVehicle(testContext);
        const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);

        const servicePayload = {
          odometer_mi: vehicle.odometer_mi + 50,
          service_notes: 'Oil change completed'
        };
        await apiClient.serviceVehicle(testContext.fleetId, vehicle.id, servicePayload, mechanicHeaders);

        const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, vehicle.id, mechanicHeaders);
        const servicedVehicle = getSuccessData(vehicleResponse);

        expect(servicedVehicle.state).toBe(VehicleState.AVAILABLE);
      });
    });

    describe('VSM-12: Vehicle NEEDS_MAINTENANCE -> NEEDS_CLEANING if dirty after service', () => {
      it('should transition to NEEDS_CLEANING if CLEAN_REQ flag remains after service', async () => {
        const { zone, vehicle } = await fixtures.setupMaintenanceOverrideVehicle(testContext);
        const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        const reportPayload = {
          vehicle_id: vehicle.id,
          report_type: ReportType.SPOT,
          cleanliness_grade: CleanlinessGrade.POOR,
          damage_grade: DamageLevel.NONE,
          mileage_recorded: vehicle.odometer_mi
        };
        await apiClient.createConditionReport(testContext.fleetId, reportPayload, operatorHeaders);

        const servicePayload = {
          odometer_mi: vehicle.odometer_mi + 50,
          service_notes: 'Maintenance completed'
        };
        await apiClient.serviceVehicle(testContext.fleetId, vehicle.id, servicePayload, mechanicHeaders);

        const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, vehicle.id, mechanicHeaders);
        const servicedVehicle = getSuccessData(vehicleResponse);

        expect(servicedVehicle.state).toBe(VehicleState.NEEDS_CLEANING);
        expect(servicedVehicle.flags).toContain(VehicleFlag.CLEAN_REQ);
      });
    });

    describe('VSM-13: ANY state -> RETIRED on decommission', () => {
      it('should permanently retire vehicle from any state', async () => {
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const retireResponse = await apiClient.retireVehicle(testContext.fleetId, vehicle.id, operatorHeaders);
        getSuccessData(retireResponse);

        const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, vehicle.id, operatorHeaders);
        const retiredVehicle = getSuccessData(vehicleResponse);

        expect(retiredVehicle.state).toBe(VehicleState.RETIRED);
      });
    });
  });

  describe('Rental State Machine', () => {
    describe('RSM-1: Rental CONFIRMED -> ACTIVE on renter unlock', () => {
      it('should activate rental on unlock', async () => {
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
        const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
        const quote = getSuccessData(quoteResponse);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
        const rental = getSuccessData(rentalResponse);

        const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
        const activeRental = getSuccessData(unlockResponse);

        expect(activeRental.state).toBe(RentalState.ACTIVE);
      });
    });

    describe('RSM-4: Rental ACTIVE -> COMPLETED on normal return', () => {
      it('should complete rental on successful return', async () => {
        const setup = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(setup.renter.id, UserRole.RENTER);

        const returnPayload = factories.returnRentalPayload(setup.zone.id, setup.vehicle.odometer_mi + 10, {
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.NONE
        });
        const returnResponse = await apiClient.returnRental(testContext.fleetId, setup.rental.id, returnPayload, renterHeaders);
        const returnedRental = getSuccessData(returnResponse);

        const userResponse = await apiClient.getUser(testContext.fleetId, setup.renter.id, renterHeaders);
        const updatedUser = getSuccessData(userResponse);

        expect(returnedRental.state).toBe(RentalState.COMPLETED);
        expect(updatedUser.active_rental_id).toBeNull();
      });
    });

    describe('RSM-5: Rental ACTIVE -> INFRACTION_REVIEW on damage return', () => {
      it('should flag rental for review on damage return', async () => {
        const setup = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(setup.renter.id, UserRole.RENTER);

        const returnPayload = factories.returnRentalPayload(setup.zone.id, setup.vehicle.odometer_mi + 10, {
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.MINOR
        });
        const returnResponse = await apiClient.returnRental(testContext.fleetId, setup.rental.id, returnPayload, renterHeaders);
        const returnedRental = getSuccessData(returnResponse);

        const userResponse = await apiClient.getUser(testContext.fleetId, setup.renter.id, renterHeaders);
        const updatedUser = getSuccessData(userResponse);

        expect(returnedRental.state).toBe(RentalState.INFRACTION_REVIEW);
        expect(updatedUser.active_rental_id).toBe(setup.rental.id);
      });
    });

    describe('RSM-6: Rental INFRACTION_REVIEW -> COMPLETED on operator resolution', () => {
      it('should complete rental and release renter after operator review', async () => {
        const setup = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(setup.renter.id, UserRole.RENTER);

        const returnPayload = factories.returnRentalPayload(setup.zone.id, setup.vehicle.odometer_mi + 10, {
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.MINOR
        });
        await apiClient.returnRental(testContext.fleetId, setup.rental.id, returnPayload, renterHeaders);

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        const reviewPayload: ReviewRentalPayload = { resolution: 'approve' };
        const reviewResponse = await apiClient.reviewRental(testContext.fleetId, setup.rental.id, reviewPayload, operatorHeaders);
        const reviewedRental = getSuccessData(reviewResponse);

        const userResponse = await apiClient.getUser(testContext.fleetId, setup.renter.id, renterHeaders);
        const updatedUser = getSuccessData(userResponse);

        expect(reviewedRental.state).toBe(RentalState.COMPLETED);
        expect(updatedUser.active_rental_id).toBeNull();
      });
    });
  });

  describe('Zone State Machine', () => {
    describe('ZSM-1: Zone OPEN -> SOFT_LOCK when surplus drops to -5', () => {
      it('should transition to SOFT_LOCK when surplus reaches -5', async () => {
        const testZone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 10 }, testContext);
        const otherZone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 10 }, testContext);

        const vehicles = [];
        for (let i = 0; i < 5; i++) {
          const vehicle = await fixtures.createTestVehicle(testContext.fleetId, testZone.id, undefined, testContext);
          vehicles.push(vehicle);
        }

        const renters = [];
        for (let i = 0; i < 5; i++) {
          const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
          renters.push(renter);

          const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
          const quotePayload = factories.quotePayload(vehicles[i].id, testZone.id, otherZone.id);
          const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
          const quote = getSuccessData(quoteResponse);

          const confirmPayload = {
            quote_id: quote.quote_id,
            quoted_price_cents: quote.price_cents
          };
          const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
          const rental = getSuccessData(rentalResponse);

          await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);

          const returnPayload = factories.returnRentalPayload(otherZone.id, vehicles[i].odometer_mi + 10);
          await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
        }

        const moreVehicles = [];
        for (let i = 0; i < 5; i++) {
          const vehicle = await fixtures.createTestVehicle(testContext.fleetId, testZone.id, undefined, testContext);
          moreVehicles.push(vehicle);
        }

        for (let i = 0; i < 5; i++) {
          const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
          const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

          const quotePayload = factories.quotePayload(moreVehicles[i].id, testZone.id, otherZone.id);
          const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
          const quote = getSuccessData(quoteResponse);

          const confirmPayload = {
            quote_id: quote.quote_id,
            quoted_price_cents: quote.price_cents
          };
          const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
          const rental = getSuccessData(rentalResponse);

          await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);

          const returnPayload = factories.returnRentalPayload(otherZone.id, moreVehicles[i].odometer_mi + 10);
          await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
        }

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        const zoneResponse = await apiClient.getParkingZone(testContext.fleetId, testZone.id, operatorHeaders);
        const updatedZone = getSuccessData(zoneResponse);

        expect(updatedZone.lock_state).toBe(ZoneLockState.HARD_LOCK);
      });
    });

    describe('ZSM-2: Zone SOFT_LOCK -> HARD_LOCK when surplus drops to -10', () => {
      it('should transition to HARD_LOCK when surplus reaches -10', async () => {
        const testZone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 10 }, testContext);
        const otherZone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 20 }, testContext);

        const vehicles = [];
        for (let i = 0; i < 10; i++) {
          const vehicle = await fixtures.createTestVehicle(testContext.fleetId, testZone.id, undefined, testContext);
          vehicles.push(vehicle);
        }

        for (let i = 0; i < 10; i++) {
          const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
          const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

          const quotePayload = factories.quotePayload(vehicles[i].id, testZone.id, otherZone.id);
          const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
          const quote = getSuccessData(quoteResponse);

          const confirmPayload = {
            quote_id: quote.quote_id,
            quoted_price_cents: quote.price_cents
          };
          const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
          const rental = getSuccessData(rentalResponse);

          await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);

          const returnPayload = factories.returnRentalPayload(otherZone.id, vehicles[i].odometer_mi + 10);
          await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
        }

        const moreVehicles = [];
        for (let i = 0; i < 10; i++) {
          const vehicle = await fixtures.createTestVehicle(testContext.fleetId, testZone.id, undefined, testContext);
          moreVehicles.push(vehicle);
        }

        for (let i = 0; i < 10; i++) {
          const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
          const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

          const quotePayload = factories.quotePayload(moreVehicles[i].id, testZone.id, otherZone.id);
          const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
          const quote = getSuccessData(quoteResponse);

          const confirmPayload = {
            quote_id: quote.quote_id,
            quoted_price_cents: quote.price_cents
          };
          const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
          const rental = getSuccessData(rentalResponse);

          await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);

          const returnPayload = factories.returnRentalPayload(otherZone.id, moreVehicles[i].odometer_mi + 10);
          await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
        }

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        const zoneResponse = await apiClient.getParkingZone(testContext.fleetId, testZone.id, operatorHeaders);
        const updatedZone = getSuccessData(zoneResponse);

        expect(updatedZone.lock_state).toBe(ZoneLockState.HARD_LOCK);
      });
    });

    describe('ZSM-3: Zone SOFT_LOCK -> OPEN when surplus recovers above -5', () => {
      it('should automatically return to OPEN when surplus improves', async () => {
        const testZone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 10 }, testContext);
        const otherZone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 10 }, testContext);

        const vehicles = [];
        for (let i = 0; i < 5; i++) {
          const vehicle = await fixtures.createTestVehicle(testContext.fleetId, testZone.id, undefined, testContext);
          vehicles.push(vehicle);
        }

        const activeRentals = [];
        for (let i = 0; i < 5; i++) {
          const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
          const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

          const quotePayload = factories.quotePayload(vehicles[i].id, testZone.id, otherZone.id);
          const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
          const quote = getSuccessData(quoteResponse);

          const confirmPayload = {
            quote_id: quote.quote_id,
            quoted_price_cents: quote.price_cents
          };
          const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
          const rental = getSuccessData(rentalResponse);

          await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);

          const returnPayload = factories.returnRentalPayload(otherZone.id, vehicles[i].odometer_mi + 10);
          await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
        }

        const moreVehicles = [];
        for (let i = 0; i < 3; i++) {
          const vehicle = await fixtures.createTestVehicle(testContext.fleetId, testZone.id, undefined, testContext);
          moreVehicles.push(vehicle);
        }

        let savedRenters = [], savedRentals = [], savedVehicles = [];
        for (let i = 0; i < 3; i++) {
          const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
          const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

          const quotePayload = factories.quotePayload(moreVehicles[i].id, testZone.id, otherZone.id);
          const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
          const quote = getSuccessData(quoteResponse);

          const confirmPayload = {
            quote_id: quote.quote_id,
            quoted_price_cents: quote.price_cents
          };
          const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
          const rental = getSuccessData(rentalResponse);

          await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);

          if (i < 2) {
            const returnPayload = factories.returnRentalPayload(otherZone.id, moreVehicles[i].odometer_mi + 10);
            await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
          } else {
            savedRenters.push(renter);
            savedRentals.push(rental);
            savedVehicles.push(moreVehicles[i]);
          }
        }

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        let zoneResponse = await apiClient.getParkingZone(testContext.fleetId, testZone.id, operatorHeaders);
        let currentZone = getSuccessData(zoneResponse);
        expect(currentZone.lock_state).toBe(ZoneLockState.SOFT_LOCK);

        const savedRenterHeaders = buildUserHeaders(savedRenters[0]!.id, UserRole.RENTER);
        const returnPayload = factories.returnRentalPayload(testZone.id, savedVehicles[0]!.odometer_mi + 10);
        await apiClient.returnRental(testContext.fleetId, savedRentals[0]!.id, returnPayload, savedRenterHeaders);

        zoneResponse = await apiClient.getParkingZone(testContext.fleetId, testZone.id, operatorHeaders);
        const updatedZone = getSuccessData(zoneResponse);

        expect(updatedZone.lock_state).toBe(ZoneLockState.OPEN);
      });
    });

    describe('ZSM-4: Zone HARD_LOCK -> OPEN requires manual operator override', () => {
      it('should require two-step manual override to unlock hard-locked zone', async () => {
        const lockedZone = await fixtures.setupHardLockedZone(testContext);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const step1Response = await apiClient.updateParkingZone(
          testContext.fleetId,
          lockedZone.id,
          { lock_state: ZoneLockState.OPEN },
          operatorHeaders
        );
        const step1Zone = getSuccessData(step1Response);

        expect(step1Zone.lock_state).toBe(ZoneLockState.OPEN);
        expect(step1Zone.manual_lock).toBe(true);

        const step2Response = await apiClient.updateParkingZone(
          testContext.fleetId,
          lockedZone.id,
          { manual_lock: false },
          operatorHeaders
        );
        const step2Zone = getSuccessData(step2Response);

        expect(step2Zone.lock_state).toBe(ZoneLockState.OPEN);
        expect(step2Zone.manual_lock).toBe(false);
      });
    });
  });
});