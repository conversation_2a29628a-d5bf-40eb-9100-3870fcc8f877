import { exec } from 'child_process';
import { URL } from 'url';

/**
 * Kill any process running on a given TCP port (defaults to 3000 or derived from API_URL).
 * Enhanced to work better on macOS.
 * Exported so helpers/test-utils.ts can import from '../setup'.
 */
export const killPort = async (port?: string): Promise<void> => {
  const apiUrl = process.env.API_URL || 'http://localhost:3000';
  const portToKill = port || new URL(apiUrl).port || '3000';

  return new Promise((resolve) => {
    // Check if lsof exists; if not, fall back to killing node processes running server.js
    exec('command -v lsof', (err) => {
      let cmd: string;
      if (!err) {
        const platform = process.platform;
        if (platform === 'darwin') {
          cmd = `lsof -i tcp:${portToKill} | grep LISTEN | awk '{print $2}' | xargs kill -9 2>/dev/null || true`;
        } else {
          cmd = `lsof -ti:${portToKill} | xargs kill -9 2>/dev/null || true`;
        }
      } else {
        // Fallback: kill any node process running server.js that might be using the port
        cmd = `ps -eo pid,command | grep 'node' | grep 'src/server.js' | awk '{print $1}' | xargs kill -9 2>/dev/null || true`;
      }

      exec(cmd, () => resolve());
    });
  });
};