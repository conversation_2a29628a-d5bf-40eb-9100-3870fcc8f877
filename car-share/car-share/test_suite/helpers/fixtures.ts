import { v4 as uuidv4 } from 'uuid';
import {
  CityFleet,
  Vehicle,
  User,
  ParkingZone,
  Rental,
  Quote,
  ConditionReport,
  MaintenanceHold,
  VehicleClass,
  VehicleState,
  UserRole,
  RentalState,
  ZoneLockState,
  FleetPhaseState,
  MaintenanceReasonCode,
  CleanlinessGrade,
  DamageLevel,
  CreateCityFleetPayload,
  CreateVehiclePayload,
  CreateUserPayload,
  CreateParkingZonePayload,
  CreateQuotePayload,
  ConfirmRentalPayload,
  ReturnRentalPayload,
  CreateMaintenanceHoldPayload,
  TestContext,
} from './types';
import { apiClient } from './api-client';
import { buildUserHeaders, generateValidUserId } from './header-utils';
import { getSuccessData } from './validators';

// Test Data Generators
export const testData = {
  // Generate unique identifiers
  generateCityCode: () => {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    return Array.from({ length: 3 }, () => letters[Math.floor(Math.random() * letters.length)]).join('');
  },

  generateZoneCode: () => {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    return `${letters[Math.floor(Math.random() * letters.length)]}${letters[Math.floor(Math.random() * letters.length)]}${letters[Math.floor(Math.random() * letters.length)]}-${numbers[Math.floor(Math.random() * numbers.length)]}${numbers[Math.floor(Math.random() * numbers.length)]}`;
  },

  generateVIN: () => {
    const chars = 'ABCDEFGHJKLMNPRSTUVWXYZ**********';
    return Array.from({ length: 17 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
  },

  generateLicensePlate: () => {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    return `${letters[Math.floor(Math.random() * letters.length)]}${letters[Math.floor(Math.random() * letters.length)]}${
      numbers[Math.floor(Math.random() * numbers.length)]
    }${numbers[Math.floor(Math.random() * numbers.length)]}${numbers[Math.floor(Math.random() * numbers.length)]}${
      numbers[Math.floor(Math.random() * numbers.length)]
    }`;
  },

  generateEmail: (prefix = 'user') => `${prefix}-${uuidv4().substring(0, 8)}@test.com`,

  generatePolygonGeoJSON: () => {
    // Simple square polygon around a random point
    const baseLat = 37.7749 + (Math.random() - 0.5) * 0.1;
    const baseLng = -122.4194 + (Math.random() - 0.5) * 0.1;
    const size = 0.01;
    
    return JSON.stringify({
      type: 'Polygon',
      coordinates: [[
        [baseLng, baseLat],
        [baseLng + size, baseLat],
        [baseLng + size, baseLat + size],
        [baseLng, baseLat + size],
        [baseLng, baseLat]
      ]]
    });
  },
};

// Factory Functions
export const factories = {
  // City Fleet Factory
  cityFleetPayload: (overrides?: Partial<CreateCityFleetPayload>): CreateCityFleetPayload => ({
    city_code: testData.generateCityCode(),
    name: `Test Fleet ${uuidv4().substring(0, 8)}`,
    allowed_vehicle_classes: [VehicleClass.ECONOMY, VehicleClass.STANDARD, VehicleClass.LUXURY, VehicleClass.EV],
    ...overrides,
  }),

  // Parking Zone Factory
  parkingZonePayload: (overrides?: Partial<CreateParkingZonePayload>): CreateParkingZonePayload => ({
    code: testData.generateZoneCode(),
    display_name: `Zone ${uuidv4().substring(0, 8)}`,
    geojson: testData.generatePolygonGeoJSON(),
    target_vehicles: 10,
    ...overrides,
  }),

  // Vehicle Factory
  vehiclePayload: (overrides?: Partial<CreateVehiclePayload>): CreateVehiclePayload => {
    const defaults = {
      class: VehicleClass.STANDARD,
      vin: testData.generateVIN(),
      license_plate: testData.generateLicensePlate(),
      service_interval_mi: 5000,
      odometer_mi: 1000,
      last_service_odometer_mi: 1000, // Default to be same as odometer_mi
      current_zone_id: '', // Will be overridden in actual usage
    };

    const payload = { ...defaults, ...overrides };

    // If odometer was overridden but last_service was not, sync them up
    // to maintain the "new vehicle" invariant by default.
    if (overrides && 'odometer_mi' in overrides && !('last_service_odometer_mi' in overrides)) {
      payload.last_service_odometer_mi = payload.odometer_mi;
    }

    return payload;
  },

  // User Factory
  userPayload: (role: UserRole, overrides?: Partial<CreateUserPayload>): CreateUserPayload => ({
    role,
    name: `Test ${role} ${uuidv4().substring(0, 8)}`,
    email: testData.generateEmail(role.toLowerCase()),
    ...overrides,
  }),

  // Quote Factory
  quotePayload: (vehicleId: string, startZoneId: string, endZoneId: string, overrides?: Partial<CreateQuotePayload>): CreateQuotePayload => ({
    vehicle_id: vehicleId,
    start_zone_id: startZoneId,
    end_zone_id: endZoneId,
    distance_estimate_mi: 10,
    ...overrides,
  }),

  // Maintenance Hold Factory
  maintenanceHoldPayload: (vehicleId: string, overrides?: Partial<CreateMaintenanceHoldPayload>): CreateMaintenanceHoldPayload => ({
    vehicle_id: vehicleId,
    reason_code: MaintenanceReasonCode.OIL_CHANGE,
    override_allowed: false,
    allowed_miles_remaining: 0,
    ...overrides,
  }),

  // Return Rental Factory
  returnRentalPayload: (endZoneId: string, endOdometer: number, overrides?: Partial<ReturnRentalPayload>): ReturnRentalPayload => ({
    end_zone_id: endZoneId,
    end_odometer_mi: endOdometer,
    cleanliness_grade: CleanlinessGrade.GOOD,
    damage_grade: DamageLevel.NONE,
    ...overrides,
  }),
};

// Fixture Setup Functions
export const fixtures = {
  // Create a basic test fleet
  createTestFleet: async (context?: TestContext) => {
    const operatorUserId = generateValidUserId();
    const operatorHeaders = buildUserHeaders(operatorUserId, UserRole.FLEET_OPERATOR);
    const fleetPayload = factories.cityFleetPayload();
    const response = await apiClient.createCityFleet(fleetPayload, operatorHeaders);
    
    const fleet = getSuccessData(response);
    
    // Store user IDs in context if provided
    if (context) {
      context.captures.operatorId = operatorUserId;
    }
    
    return fleet;
  },

  // Create a test zone in a fleet
  createTestZone: async (fleetId: string, overrides?: Partial<CreateParkingZonePayload>, context?: TestContext) => {
    // Use stored operator ID if available, otherwise generate new one
    const operatorUserId = context?.captures?.operatorId || generateValidUserId();
    const operatorHeaders = buildUserHeaders(operatorUserId, UserRole.FLEET_OPERATOR);
    const zonePayload = factories.parkingZonePayload(overrides);
    const response = await apiClient.createParkingZone(fleetId, zonePayload, operatorHeaders);
    
    return getSuccessData(response);
  },

  // Create a test vehicle in a zone
  createTestVehicle: async (fleetId: string, zoneId: string, overrides?: Partial<CreateVehiclePayload>, context?: TestContext) => {
    // Generate a mechanic ID if not stored, but ensure they're part of the fleet
    let mechanicUserId = context?.captures?.mechanicId;
    if (!mechanicUserId) {
      // Create a mechanic user first if we don't have one
      const operatorUserId = context?.captures?.operatorId || generateValidUserId();
      const operatorHeaders = buildUserHeaders(operatorUserId, UserRole.FLEET_OPERATOR);
      const mechanicUser = await fixtures.createTestUser(fleetId, UserRole.MECHANIC, undefined, context);
      mechanicUserId = mechanicUser.id;
      if (context) {
        context.captures.mechanicId = mechanicUserId;
      }
    }
    
    const mechanicHeaders = buildUserHeaders(mechanicUserId, UserRole.MECHANIC);
    const vehiclePayload = factories.vehiclePayload({
      current_zone_id: zoneId,
      ...overrides,
    });
    const response = await apiClient.createVehicle(fleetId, vehiclePayload, mechanicHeaders);
    
    return getSuccessData(response);
  },

  // Create a test user
  createTestUser: async (fleetId: string, role: UserRole, overrides?: Partial<CreateUserPayload>, context?: TestContext) => {
    // Use stored operator ID to create users
    const operatorUserId = context?.captures?.operatorId || generateValidUserId();
    const operatorHeaders = buildUserHeaders(operatorUserId, UserRole.FLEET_OPERATOR);
    const userPayload = factories.userPayload(role, overrides);
    const response = await apiClient.createUser(fleetId, userPayload, operatorHeaders);
    
    return getSuccessData(response);
  },

  // Complex Fixture Setups
  
  // Setup: Basic Available Vehicle
  setupBasicAvailableVehicle: async (context: TestContext) => {
    const zone = await fixtures.createTestZone(context.fleetId, undefined, context);
    const vehicle = await fixtures.createTestVehicle(context.fleetId, zone.id, undefined, context);
    
    context.captures.zoneId = zone.id;
    context.captures.vehicleId = vehicle.id;
    
    return { zone, vehicle };
  },

  // Setup: Maintenance Override Vehicle
  setupMaintenanceOverrideVehicle: async (context: TestContext) => {
    const zone = await fixtures.createTestZone(context.fleetId, undefined, context);
    // Create a vehicle that is DUE for service.
    // Per the PRD, we can't create it in this state. We must create it validly
    // and then update it. The easiest way is to set a low service interval.
    const vehicle = await fixtures.createTestVehicle(context.fleetId, zone.id, {
      odometer_mi: 5000,
      last_service_odometer_mi: 5000,
      service_interval_mi: 1000, // This makes miles_until_service < 0
    }, context);
    
    const mechanicUserId = context.captures.mechanicId || generateValidUserId();
    const mechanicHeaders = buildUserHeaders(mechanicUserId, UserRole.MECHANIC);
    const holdPayload = factories.maintenanceHoldPayload(vehicle.id, {
      reason_code: MaintenanceReasonCode.OIL_CHANGE,
      override_allowed: true,
      allowed_miles_remaining: 50,
    });
    
    const holdResponse = await apiClient.createMaintenanceHold(context.fleetId, holdPayload, mechanicHeaders);
    const hold = getSuccessData(holdResponse);
    
    context.captures.zoneId = zone.id;
    context.captures.vehicleId = vehicle.id;
    context.captures.holdId = hold.id;
    
    return { zone, vehicle, hold };
  },

  // Setup: Suspended Renter
  setupSuspendedRenter: async (context: TestContext) => {
    const renter = await fixtures.createTestUser(context.fleetId, UserRole.RENTER, undefined, context);
    
    const operatorHeaders = buildUserHeaders(context.captures.operatorId!, UserRole.FLEET_OPERATOR);
    const updateResponse = await apiClient.updateUser(
      context.fleetId,
      renter.id,
      { suspended: true },
      operatorHeaders
    );
    
    const suspendedRenter = getSuccessData(updateResponse);
    context.captures.renterId = renter.id;
    
    return suspendedRenter;
  },

  // Setup: Hard Locked Zone
  setupHardLockedZone: async (context: TestContext) => {
    const zone = await fixtures.createTestZone(context.fleetId, {
      target_vehicles: 1,
    }, context);
    
    const operatorHeaders = buildUserHeaders(context.captures.operatorId!, UserRole.FLEET_OPERATOR);
    const updateResponse = await apiClient.updateParkingZone(
      context.fleetId,
      zone.id,
      { lock_state: ZoneLockState.HARD_LOCK },
      operatorHeaders
    );
    
    const lockedZone = getSuccessData(updateResponse);
    context.captures.zoneId = zone.id;
    
    return lockedZone;
  },

  // Setup: Active Rental Scenario
  setupActiveRentalScenario: async (context: TestContext) => {
    // Create zone, vehicle, and renter
    const zone = await fixtures.createTestZone(context.fleetId, undefined, context);
    const vehicle = await fixtures.createTestVehicle(context.fleetId, zone.id, {
      class: VehicleClass.ECONOMY,
    }, context);
    const renter = await fixtures.createTestUser(context.fleetId, UserRole.RENTER, undefined, context);
    
    // Create quote
    const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
    const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id, {
      distance_estimate_mi: 10,
    });
    
    const quoteResponse = await apiClient.createQuote(context.fleetId, quotePayload, renterHeaders);
    const quote = getSuccessData(quoteResponse);
    
    // Confirm rental
    const confirmPayload: ConfirmRentalPayload = {
      quote_id: quote.quote_id,
      quoted_price_cents: quote.price_cents
    };
    
    const rentalResponse = await apiClient.confirmRental(context.fleetId, confirmPayload, renterHeaders);
    const rental = getSuccessData(rentalResponse);
    
    // Unlock rental (activate it)
    const unlockResponse = await apiClient.unlockRental(
      context.fleetId,
      rental.id,
      renterHeaders
    );
    const unlockedRental = getSuccessData(unlockResponse);
    
    context.captures.zoneId = zone.id;
    context.captures.vehicleId = vehicle.id;
    context.captures.renterId = renter.id;
    context.captures.rentalId = unlockedRental.id;
    context.captures.quoteId = quote.quote_id;
    
    return {
      zone,
      vehicle,
      renter,
      quote,
      rental: unlockedRental,
    };
  },

  // Setup: High Reputation Renter
  setupHighRepRenter: async (context: TestContext) => {
    const renter = await fixtures.createTestUser(context.fleetId, UserRole.RENTER, undefined, context);
    const operatorHeaders = buildUserHeaders(context.captures.operatorId!, UserRole.FLEET_OPERATOR);
    
    await apiClient.updateUser(
      context.fleetId,
      renter.id,
      { reputation_score: 200 },
      operatorHeaders
    );

    const updatedRenterResponse = await apiClient.getUser(context.fleetId, renter.id, operatorHeaders);
    const updatedRenter = getSuccessData(updatedRenterResponse);

    context.captures.renterId = updatedRenter.id;
    return updatedRenter;
  },

  // Setup: Low Reputation Renter
  setupLowRepRenter: async (context: TestContext) => {
    const renter = await fixtures.createTestUser(context.fleetId, UserRole.RENTER, undefined, context);
    const operatorHeaders = buildUserHeaders(context.captures.operatorId!, UserRole.FLEET_OPERATOR);
    
    await apiClient.updateUser(
      context.fleetId,
      renter.id,
      { reputation_score: 50 },
      operatorHeaders
    );

    const updatedRenterResponse = await apiClient.getUser(context.fleetId, renter.id, operatorHeaders);
    const updatedRenter = getSuccessData(updatedRenterResponse);
    
    context.captures.renterId = updatedRenter.id;
    return updatedRenter;
  },

  // Setup: Fleet in HIGH_ALERT
  setupHighAlertFleet: async (context: TestContext) => {
    // Create multiple zones in hard lock to trigger HIGH_ALERT
    const zones = await Promise.all([
      fixtures.createTestZone(context.fleetId, { target_vehicles: 20 }, context),
      fixtures.createTestZone(context.fleetId, { target_vehicles: 20 }, context),
      fixtures.createTestZone(context.fleetId, { target_vehicles: 20 }, context),
    ]);
    
    const operatorHeaders = buildUserHeaders(context.captures.operatorId!, UserRole.FLEET_OPERATOR);
    
    // Hard lock all zones
    await Promise.all(
      zones.map(zone =>
        apiClient.updateParkingZone(
          context.fleetId,
          zone.id,
          { lock_state: ZoneLockState.HARD_LOCK },
          operatorHeaders
        )
      )
    );
    
    // The fleet should now be in HIGH_ALERT
    return zones;
  },
};

// Cleanup utilities
export const cleanup = {
  // Clean up a rental (cancel if confirmed, or let it expire)
  cleanupRental: async (fleetId: string, rentalId: string, renterId: string) => {
    const renterHeaders = buildUserHeaders(renterId, UserRole.RENTER);
    await apiClient.cancelRental(fleetId, rentalId, renterHeaders);
  },

  // Clean up a vehicle (retire it)
  cleanupVehicle: async (fleetId: string, vehicleId: string, context?: TestContext) => {
    const operatorUserId = context?.captures?.operatorId || generateValidUserId();
    const operatorHeaders = buildUserHeaders(operatorUserId, UserRole.FLEET_OPERATOR);
    await apiClient.retireVehicle(fleetId, vehicleId, operatorHeaders);
  },

  // Release a maintenance hold
  cleanupMaintenanceHold: async (fleetId: string, holdId: string, context?: TestContext) => {
    const mechanicUserId = context?.captures?.mechanicId || generateValidUserId();
    const mechanicHeaders = buildUserHeaders(mechanicUserId, UserRole.MECHANIC);
    await apiClient.releaseMaintenanceHold(fleetId, holdId, mechanicHeaders);
  },
};

// Derivation calculators (based on test_suite.yaml derivations)
export const derivations = {
  // Calculate quoted action duration
  quotedActionDuration: (distanceEstimateMi: number): number => {
    return 20 + Math.floor(distanceEstimateMi / 4);
  },

  // Calculate surge price adjustment
  surgePriceAdjustment: (basePriceCents: number, phaseState: FleetPhaseState): number => {
    if (phaseState === FleetPhaseState.HIGH_ALERT) {
      return Math.round(basePriceCents * 1.15); // 15% surcharge
    }
    return basePriceCents;
  },

  // Calculate miles until service
  milesUntilService: (odometerMi: number, lastServiceOdometerMi: number, serviceIntervalMi: number): number => {
    return serviceIntervalMi - (odometerMi - lastServiceOdometerMi);
  },

  // Check luxury vehicle access
  luxuryVehicleAccess: (reputationScore: number): boolean => {
    return reputationScore >= 150;
  },

  // Check if rental is late
  rentalLatenessCheck: (currentFleetActionCounter: number, startActionCount: number, quotedActionDuration: number): boolean => {
    return currentFleetActionCounter > startActionCount + quotedActionDuration;
  },

  // Calculate zone surplus
  zoneSurplusCalculation: (zoneTarget: number, vehiclesPresent: number): number => {
    return vehiclesPresent - zoneTarget;
  },
};