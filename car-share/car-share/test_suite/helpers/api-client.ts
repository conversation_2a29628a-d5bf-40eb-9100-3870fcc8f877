import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import {
  ApiResponse,
  CityFleet,
  Vehicle,
  User,
  ParkingZone,
  Rental,
  Quote,
  ConditionReport,
  MaintenanceHold,
  CreateCityFleetPayload,
  CreateParkingZonePayload,
  UpdateParkingZonePayload,
  CreateVehiclePayload,
  CreateUserPayload,
  UpdateUserPayload,
  CreateQuotePayload,
  ConfirmRentalPayload,
  UnlockRentalPayload,
  ReturnRentalPayload,
  ReviewRentalPayload,
  CreateMaintenanceHoldPayload,
  CreateConditionReportPayload,
  ServiceVehiclePayload,
  ChargeVehiclePayload,
  CleanVehiclePayload,
  FleetSummary,
  RenterSummary,
  ZoneBalanceLedgerEntry,
  VehicleMileageLedgerEntry,
  RenterReputationLedgerEntry,
} from './types';

// Base URL from environment or default
const BASE_URL = process.env.API_URL || 'http://localhost:3000';

// Header types
export interface ApiHeaders {
  'X-User-ID': string;
  'X-User-Role': string;
  'Content-Type'?: string;
  [key: string]: string | undefined;
}

// Generic request function
async function makeRequest<T>(
  method: string,
  path: string,
  headers?: ApiHeaders,
  data?: any,
  params?: Record<string, any>
): Promise<AxiosResponse<ApiResponse<T>>> {
  const config: AxiosRequestConfig = {
    method,
    url: `${BASE_URL}${path}`,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    data,
    params,
    validateStatus: () => true, // Don't throw on any status code
  };

  const response: AxiosResponse<ApiResponse<T>> = await axios(config);
  return response;
}

// API Client with all car-share endpoints
export const apiClient = {
  // City Fleet Management
  createCityFleet: (data: CreateCityFleetPayload, headers: ApiHeaders) =>
    makeRequest<CityFleet>('POST', '/city-fleets', headers, data),

  getCityFleet: (fleetId: string, headers: ApiHeaders) =>
    makeRequest<CityFleet>('GET', `/city-fleets/${fleetId}`, headers),

  getFleetSummary: (fleetId: string, headers: ApiHeaders) =>
    makeRequest<FleetSummary | RenterSummary>('GET', `/city-fleets/${fleetId}/summary`, headers),

  // Parking Zone Management
  createParkingZone: (fleetId: string, data: CreateParkingZonePayload, headers: ApiHeaders) =>
    makeRequest<ParkingZone>('POST', `/city-fleets/${fleetId}/parking-zones`, headers, data),

  getParkingZone: (fleetId: string, zoneId: string, headers: ApiHeaders) =>
    makeRequest<ParkingZone>('GET', `/city-fleets/${fleetId}/parking-zones/${zoneId}`, headers),

  updateParkingZone: (fleetId: string, zoneId: string, data: UpdateParkingZonePayload, headers: ApiHeaders) =>
    makeRequest<ParkingZone>('PATCH', `/city-fleets/${fleetId}/parking-zones/${zoneId}`, headers, data),

  // No list parking zones endpoint in the blueprint

  // Vehicle Management
  createVehicle: (fleetId: string, data: CreateVehiclePayload, headers: ApiHeaders) =>
    makeRequest<Vehicle>('POST', `/city-fleets/${fleetId}/vehicles`, headers, data),

  getVehicle: (fleetId: string, vehicleId: string, headers: ApiHeaders) =>
    makeRequest<Vehicle>('GET', `/city-fleets/${fleetId}/vehicles/${vehicleId}`, headers),

  retireVehicle: (fleetId: string, vehicleId: string, headers: ApiHeaders) =>
    makeRequest<Vehicle>('POST', `/city-fleets/${fleetId}/vehicles/${vehicleId}/retire`, headers),

  serviceVehicle: (fleetId: string, vehicleId: string, data: ServiceVehiclePayload, headers: ApiHeaders) =>
    makeRequest<Vehicle>('POST', `/city-fleets/${fleetId}/vehicles/${vehicleId}/service`, headers, data),

  chargeVehicle: (fleetId: string, vehicleId: string, data: ChargeVehiclePayload, headers: ApiHeaders) =>
    makeRequest<Vehicle>('POST', `/city-fleets/${fleetId}/vehicles/${vehicleId}/charge`, headers, data),

  updateVehicleServiceStatus: (fleetId: string, vehicleId: string, data: CleanVehiclePayload, headers: ApiHeaders) =>
    makeRequest<Vehicle>('PATCH', `/city-fleets/${fleetId}/vehicles/${vehicleId}/service-status`, headers, data),

  // User Management
  createUser: (fleetId: string, data: CreateUserPayload, headers: ApiHeaders) =>
    makeRequest<User>('POST', `/city-fleets/${fleetId}/users`, headers, data),

  getUser: (fleetId: string, userId: string, headers: ApiHeaders) =>
    makeRequest<User>('GET', `/city-fleets/${fleetId}/users/${userId}`, headers),

  updateUser: (fleetId: string, userId: string, data: UpdateUserPayload, headers: ApiHeaders) =>
    makeRequest<User>('PATCH', `/city-fleets/${fleetId}/users/${userId}`, headers, data),

  // No list users endpoint in the blueprint

  // Rental Management
  createQuote: (fleetId: string, data: CreateQuotePayload, headers: ApiHeaders) =>
    makeRequest<Quote>('POST', `/city-fleets/${fleetId}/rentals/quote`, headers, data),

  deleteQuote: (fleetId: string, quoteId: string, headers: ApiHeaders) =>
    makeRequest<void>('DELETE', `/city-fleets/${fleetId}/rentals/quote/${quoteId}`, headers),

  confirmRental: (fleetId: string, data: ConfirmRentalPayload, headers: ApiHeaders) =>
    makeRequest<Rental>('POST', `/city-fleets/${fleetId}/rentals`, headers, data),

  getRental: (fleetId: string, rentalId: string, headers: ApiHeaders) =>
    makeRequest<Rental>('GET', `/city-fleets/${fleetId}/rentals/${rentalId}`, headers),

  unlockRental: (fleetId: string, rentalId: string, headers: ApiHeaders) =>
    makeRequest<Rental>('PATCH', `/city-fleets/${fleetId}/rentals/${rentalId}`, headers, { action: 'unlock' }),

  cancelRental: (fleetId: string, rentalId: string, headers: ApiHeaders) =>
    makeRequest<Rental>('DELETE', `/city-fleets/${fleetId}/rentals/${rentalId}`, headers),

  returnRental: (fleetId: string, rentalId: string, data: ReturnRentalPayload, headers: ApiHeaders) =>
    makeRequest<Rental>('PATCH', `/city-fleets/${fleetId}/rentals/${rentalId}/return`, headers, data),

  reviewRental: (fleetId: string, rentalId: string, data: ReviewRentalPayload, headers: ApiHeaders) =>
    makeRequest<Rental>('PATCH', `/city-fleets/${fleetId}/rentals/${rentalId}/review`, headers, data),

  // Maintenance Management
  createMaintenanceHold: (fleetId: string, data: CreateMaintenanceHoldPayload, headers: ApiHeaders) =>
    makeRequest<MaintenanceHold>('POST', `/city-fleets/${fleetId}/maintenance-holds`, headers, data),

  getMaintenanceHold: (fleetId: string, holdId: string, headers: ApiHeaders) =>
    makeRequest<MaintenanceHold>('GET', `/city-fleets/${fleetId}/maintenance-holds/${holdId}`, headers),

  releaseMaintenanceHold: (fleetId: string, holdId: string, headers: ApiHeaders) =>
    makeRequest<MaintenanceHold>('PATCH', `/city-fleets/${fleetId}/maintenance-holds/${holdId}/release`, headers),

  listMaintenanceHolds: (
    fleetId: string,
    params: { vehicle_id?: string },
    headers: ApiHeaders
  ) =>
    makeRequest<MaintenanceHold[]>('GET', `/city-fleets/${fleetId}/maintenance-holds`, headers, null, params),

  // Condition Reports
  createConditionReport: (fleetId: string, data: CreateConditionReportPayload, headers: ApiHeaders) =>
    makeRequest<ConditionReport>('POST', `/city-fleets/${fleetId}/condition-reports`, headers, data),

  getConditionReport: (fleetId: string, reportId: string, headers: ApiHeaders) =>
    makeRequest<ConditionReport>('GET', `/city-fleets/${fleetId}/condition-reports/${reportId}`, headers),

  // Ledger Endpoints
  getZoneBalanceLedger: (fleetId: string, headers: ApiHeaders) =>
    makeRequest<ZoneBalanceLedgerEntry[]>('GET', `/city-fleets/${fleetId}/ledgers/zone-balance`, headers),

  getRenterReputationLedger: (fleetId: string, renterId: string, headers: ApiHeaders) =>
    makeRequest<RenterReputationLedgerEntry>('GET', `/city-fleets/${fleetId}/ledgers/renter/${renterId}/reputation`, headers),

  getVehicleMileageLedger: (fleetId: string, vehicleId: string, headers: ApiHeaders) =>
    makeRequest<VehicleMileageLedgerEntry>('GET', `/city-fleets/${fleetId}/ledgers/vehicle/${vehicleId}/mileage`, headers),

  // Raw request for custom endpoints
  raw: makeRequest,
};

export default apiClient;