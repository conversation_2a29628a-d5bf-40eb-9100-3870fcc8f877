import {
  CityFleet,
  Vehicle,
  User,
  ParkingZone,
  Ren<PERSON>,
  Quote,
  ConditionReport,
  MaintenanceHold,
  VehicleClass,
  VehicleState,
  UserRole,
  RentalState,
  ZoneLockState,
  FleetPhaseState,
  MaintenanceReasonCode,
  CleanlinessGrade,
  DamageLevel,
} from './types';

// ID Pattern Constants
const ID_PATTERNS = {
  FLEET: /^CF-[A-Z0-9]{6}$/,
  VEHICLE: /^VH-[A-Z0-9]{6}$/,
  USER: /^UR-[A-Z0-9]{6}$/,
  ZONE: /^PZ-[A-Z0-9]{6}$/,
  RENTAL: /^RT-[A-Z0-9]{6}$/,
  QUOTE: /^QT-[A-Z0-9]{6}$/,
  REPORT: /^CR-[A-Z0-9]{6}$/,
  HOLD: /^MH-[A-Z0-9]{6}$/,
};

// Validation Constants
const VALIDATION_RULES = {
  CITY_CODE: /^[A-Z]{2,3}$/,
  ZONE_CODE: /^[A-Z0-9-]{2,10}$/,
  VIN: /^[A-HJ-NPR-Z0-9]{17}$/,
  LICENSE_PLATE: /^[A-Z0-9]{1,8}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  MIN_NAME_LENGTH: 3,
  MAX_NAME_LENGTH: 50,
  MIN_REPUTATION: 0,
  MAX_REPUTATION: 300,
  MIN_BATTERY_PCT: 0,
  MAX_BATTERY_PCT: 100,
  MIN_ODOMETER: 0,
  MAX_ODOMETER: 999999,
  MIN_SERVICE_INTERVAL: 1000,
  MAX_SERVICE_INTERVAL: 50000,
};

// Helper function to validate ID format
function assertIdFormat(id: string, type: keyof typeof ID_PATTERNS, fieldName: string): void {
  if (!ID_PATTERNS[type].test(id)) {
    throw new Error(`${fieldName} does not match expected pattern ${ID_PATTERNS[type]}`);
  }
}

// Helper function to validate ISO timestamp
function assertISOTimestamp(timestamp: string, fieldName: string): void {
  const date = new Date(timestamp);
  if (isNaN(date.getTime())) {
    throw new Error(`${fieldName} is not a valid ISO timestamp`);
  }
}

// Helper function to validate enum value
function assertEnumValue<T>(value: any, enumObj: Record<string, T>, fieldName: string): void {
  if (!Object.values(enumObj).includes(value)) {
    throw new Error(`${fieldName} has invalid value: ${value}. Expected one of: ${Object.values(enumObj).join(', ')}`);
  }
}

// City Fleet Assertion
export function assertCityFleet(fleet: any): asserts fleet is CityFleet {
  if (!fleet || typeof fleet !== 'object') {
    throw new Error('Fleet must be an object');
  }

  // Required fields
  assertIdFormat(fleet.id, 'FLEET', 'Fleet ID');
  
  if (!VALIDATION_RULES.CITY_CODE.test(fleet.city_code)) {
    throw new Error('City code must be 2-3 uppercase letters');
  }
  
  if (typeof fleet.name !== 'string' || fleet.name.length < 3 || fleet.name.length > 50) {
    throw new Error('Fleet name must be 3-50 characters');
  }
  
  if (!Array.isArray(fleet.allowed_vehicle_classes)) {
    throw new Error('Allowed vehicle classes must be an array');
  }
  
  fleet.allowed_vehicle_classes.forEach((vc: any) => {
    assertEnumValue(vc, VehicleClass, 'Vehicle class');
  });
  
  assertEnumValue(fleet.phase_state, FleetPhaseState, 'Phase state');
  
  if (typeof fleet.action_counter !== 'number' || fleet.action_counter < 0) {
    throw new Error('Action counter must be a non-negative number');
  }
  
  assertISOTimestamp(fleet.created_at, 'Created at');
  assertISOTimestamp(fleet.updated_at, 'Updated at');
}

// Vehicle Assertion
export function assertVehicle(vehicle: any): asserts vehicle is Vehicle {
  if (!vehicle || typeof vehicle !== 'object') {
    throw new Error('Vehicle must be an object');
  }

  // Required fields
  assertIdFormat(vehicle.id, 'VEHICLE', 'Vehicle ID');
  assertIdFormat(vehicle.fleet_id, 'FLEET', 'Fleet ID');
  
  assertEnumValue(vehicle.class, VehicleClass, 'Vehicle class');
  
  if (!VALIDATION_RULES.VIN.test(vehicle.vin)) {
    throw new Error('VIN must be a valid 17-character VIN');
  }
  
  if (!VALIDATION_RULES.LICENSE_PLATE.test(vehicle.license_plate)) {
    throw new Error('License plate must be 1-8 alphanumeric characters');
  }
  
  assertEnumValue(vehicle.state, VehicleState, 'Vehicle state');
  
  if (vehicle.current_zone_id !== null) {
    assertIdFormat(vehicle.current_zone_id, 'ZONE', 'Current zone ID');
  }
  
  if (typeof vehicle.odometer_mi !== 'number' || 
      vehicle.odometer_mi < VALIDATION_RULES.MIN_ODOMETER || 
      vehicle.odometer_mi > VALIDATION_RULES.MAX_ODOMETER) {
    throw new Error('Odometer must be between 0 and 999999');
  }
  
  if (typeof vehicle.service_interval_mi !== 'number' || 
      vehicle.service_interval_mi < VALIDATION_RULES.MIN_SERVICE_INTERVAL || 
      vehicle.service_interval_mi > VALIDATION_RULES.MAX_SERVICE_INTERVAL) {
    throw new Error('Service interval must be between 1000 and 50000');
  }
  
  if (typeof vehicle.last_service_odometer_mi !== 'number' || vehicle.last_service_odometer_mi < 0) {
    throw new Error('Last service odometer must be non-negative');
  }
  
  if (typeof vehicle.miles_until_service !== 'number') {
    throw new Error('Miles until service must be a number');
  }
  
  // EV-specific validation
  if (vehicle.class === VehicleClass.EV) {
    if (typeof vehicle.battery_pct !== 'number' || 
        vehicle.battery_pct < VALIDATION_RULES.MIN_BATTERY_PCT || 
        vehicle.battery_pct > VALIDATION_RULES.MAX_BATTERY_PCT) {
      throw new Error('EV vehicles must have battery percentage between 0 and 100');
    }
  }
  
  if (!Array.isArray(vehicle.flags)) {
    throw new Error('Vehicle flags must be an array');
  }
  
  assertISOTimestamp(vehicle.created_at, 'Created at');
  assertISOTimestamp(vehicle.updated_at, 'Updated at');
}

// User Assertion
export function assertUser(user: any): asserts user is User {
  if (!user || typeof user !== 'object') {
    throw new Error('User must be an object');
  }

  // Required fields
  assertIdFormat(user.id, 'USER', 'User ID');
  assertIdFormat(user.fleet_id, 'FLEET', 'Fleet ID');
  
  assertEnumValue(user.role, UserRole, 'User role');
  
  if (typeof user.name !== 'string' || 
      user.name.length < VALIDATION_RULES.MIN_NAME_LENGTH || 
      user.name.length > VALIDATION_RULES.MAX_NAME_LENGTH) {
    throw new Error('User name must be 3-50 characters');
  }
  
  if (!VALIDATION_RULES.EMAIL.test(user.email)) {
    throw new Error('Email must be a valid email address');
  }
  
  // Renter-specific fields
  if (user.role === UserRole.RENTER) {
    if (typeof user.reputation_score !== 'number' || 
        user.reputation_score < VALIDATION_RULES.MIN_REPUTATION || 
        user.reputation_score > VALIDATION_RULES.MAX_REPUTATION) {
      throw new Error('Renter reputation score must be between 0 and 300');
    }
    
    if (typeof user.probationary !== 'boolean') {
      throw new Error('Renter probationary status must be a boolean');
    }
    
    if (typeof user.luxury_unlock !== 'boolean') {
      throw new Error('Renter luxury unlock status must be a boolean');
    }
    
    if (typeof user.late_return_count !== 'number' || user.late_return_count < 0) {
      throw new Error('Late return count must be non-negative');
    }
    
    if (user.active_rental_id !== null && typeof user.active_rental_id !== 'string') {
      throw new Error('Active rental ID must be null or string');
    }
    
    if (user.active_rental_id && !ID_PATTERNS.RENTAL.test(user.active_rental_id)) {
      throw new Error('Active rental ID has invalid format');
    }
  }
  
  if (typeof user.suspended !== 'boolean') {
    throw new Error('Suspended status must be a boolean');
  }
  
  assertISOTimestamp(user.created_at, 'Created at');
  assertISOTimestamp(user.updated_at, 'Updated at');
}

// Parking Zone Assertion
export function assertParkingZone(zone: any): asserts zone is ParkingZone {
  if (!zone || typeof zone !== 'object') {
    throw new Error('Parking zone must be an object');
  }

  // Required fields
  assertIdFormat(zone.id, 'ZONE', 'Zone ID');
  assertIdFormat(zone.fleet_id, 'FLEET', 'Fleet ID');
  
  if (!VALIDATION_RULES.ZONE_CODE.test(zone.code)) {
    throw new Error('Zone code must be 2-10 alphanumeric characters with hyphens');
  }
  
  if (typeof zone.display_name !== 'string' || zone.display_name.length < 3 || zone.display_name.length > 50) {
    throw new Error('Zone display name must be 3-50 characters');
  }
  
  if (typeof zone.geojson !== 'string') {
    throw new Error('Zone geojson must be a string');
  }
  
  if (typeof zone.target_vehicles !== 'number' || zone.target_vehicles < 0) {
    throw new Error('Target vehicles must be non-negative');
  }
  
  assertEnumValue(zone.lock_state, ZoneLockState, 'Lock state');
  
  if (zone.vehicle_incentive_cents !== undefined) {
    if (typeof zone.vehicle_incentive_cents !== 'number') {
      throw new Error('Vehicle incentive must be a number');
    }
  }
  
  if (zone.discouraged_vehicle_classes !== undefined) {
    if (!Array.isArray(zone.discouraged_vehicle_classes)) {
      throw new Error('Discouraged vehicle classes must be an array');
    }
    zone.discouraged_vehicle_classes.forEach((vc: any) => {
      assertEnumValue(vc, VehicleClass, 'Discouraged vehicle class');
    });
  }
  
  assertISOTimestamp(zone.created_at, 'Created at');
  assertISOTimestamp(zone.updated_at, 'Updated at');
}

// Rental Assertion
export function assertRental(rental: any): asserts rental is Rental {
  if (!rental || typeof rental !== 'object') {
    throw new Error('Rental must be an object');
  }

  // Required fields
  assertIdFormat(rental.id, 'RENTAL', 'Rental ID');
  assertIdFormat(rental.fleet_id, 'FLEET', 'Fleet ID');
  assertIdFormat(rental.vehicle_id, 'VEHICLE', 'Vehicle ID');
  assertIdFormat(rental.renter_id, 'USER', 'Renter ID');
  
  assertEnumValue(rental.state, RentalState, 'Rental state');
  
  assertIdFormat(rental.start_zone_id, 'ZONE', 'Start zone ID');
  assertIdFormat(rental.end_zone_id, 'ZONE', 'End zone ID');
  
  if (typeof rental.distance_estimate_mi !== 'number' || rental.distance_estimate_mi < 0) {
    throw new Error('Distance estimate must be non-negative');
  }
  
  if (typeof rental.price_cents !== 'number' || rental.price_cents < 0) {
    throw new Error('Price must be non-negative');
  }
  
  if (typeof rental.start_action_count !== 'number' || rental.start_action_count < 0) {
    throw new Error('Start action count must be non-negative');
  }
  
  if (typeof rental.quoted_action_duration !== 'number' || rental.quoted_action_duration < 0) {
    throw new Error('Quoted action duration must be non-negative');
  }
  
  // Optional fields for active/completed rentals
  if (rental.start_odometer_mi !== undefined) {
    if (typeof rental.start_odometer_mi !== 'number' || rental.start_odometer_mi < 0) {
      throw new Error('Start odometer must be non-negative');
    }
  }
  
  if (rental.end_odometer_mi !== undefined) {
    if (typeof rental.end_odometer_mi !== 'number' || rental.end_odometer_mi < 0) {
      throw new Error('End odometer must be non-negative');
    }
  }
  
  if (rental.condition_report_id !== undefined) {
    assertIdFormat(rental.condition_report_id, 'REPORT', 'Condition report ID');
  }
  
  assertISOTimestamp(rental.created_at, 'Created at');
  assertISOTimestamp(rental.updated_at, 'Updated at');
}

// Quote Assertion
export function assertQuote(quote: any): asserts quote is Quote {
  if (!quote || typeof quote !== 'object') {
    throw new Error('Quote must be an object');
  }

  // Required fields
  assertIdFormat(quote.quote_id, 'QUOTE', 'Quote ID');
  assertIdFormat(quote.fleet_id, 'FLEET', 'Fleet ID');
  assertIdFormat(quote.vehicle_id, 'VEHICLE', 'Vehicle ID');
  assertIdFormat(quote.renter_id, 'USER', 'Renter ID');
  assertIdFormat(quote.start_zone_id, 'ZONE', 'Start zone ID');
  assertIdFormat(quote.end_zone_id, 'ZONE', 'End zone ID');
  
  if (typeof quote.distance_estimate_mi !== 'number' || quote.distance_estimate_mi < 0) {
    throw new Error('Distance estimate must be non-negative');
  }
  
  if (typeof quote.price_cents !== 'number' || quote.price_cents < 0) {
    throw new Error('Price must be non-negative');
  }
  
  if (typeof quote.quoted_action_duration !== 'number' || quote.quoted_action_duration < 0) {
    throw new Error('Quoted action duration must be non-negative');
  }
  
  assertISOTimestamp(quote.expires_at, 'Expires at');
  assertISOTimestamp(quote.created_at, 'Created at');
}

// Condition Report Assertion
export function assertConditionReport(report: any): asserts report is ConditionReport {
  if (!report || typeof report !== 'object') {
    throw new Error('Condition report must be an object');
  }

  // Required fields
  assertIdFormat(report.id, 'REPORT', 'Report ID');
  assertIdFormat(report.fleet_id, 'FLEET', 'Fleet ID');
  assertIdFormat(report.vehicle_id, 'VEHICLE', 'Vehicle ID');
  assertIdFormat(report.rental_id, 'RENTAL', 'Rental ID');
  
  assertEnumValue(report.cleanliness, CleanlinessGrade, 'Cleanliness');
  assertEnumValue(report.damage_level, DamageLevel, 'Damage level');
  
  if (report.damage_notes !== undefined && typeof report.damage_notes !== 'string') {
    throw new Error('Damage notes must be a string');
  }
  
  assertISOTimestamp(report.created_at, 'Created at');
}

// Maintenance Hold Assertion
export function assertMaintenanceHold(hold: any): asserts hold is MaintenanceHold {
  if (!hold || typeof hold !== 'object') {
    throw new Error('Maintenance hold must be an object');
  }

  // Required fields
  assertIdFormat(hold.id, 'HOLD', 'Hold ID');
  assertIdFormat(hold.fleet_id, 'FLEET', 'Fleet ID');
  assertIdFormat(hold.vehicle_id, 'VEHICLE', 'Vehicle ID');
  
  assertEnumValue(hold.reason_code, MaintenanceReasonCode, 'Reason code');
  
  if (hold.reason_details !== undefined && typeof hold.reason_details !== 'string') {
    throw new Error('Reason details must be a string');
  }
  
  if (typeof hold.override_allowed !== 'boolean') {
    throw new Error('Override allowed must be a boolean');
  }
  
  if (hold.allowed_miles_remaining !== undefined) {
    if (typeof hold.allowed_miles_remaining !== 'number' || hold.allowed_miles_remaining < 0) {
      throw new Error('Allowed miles remaining must be non-negative');
    }
  }
  
  if (typeof hold.active !== 'boolean') {
    throw new Error('Active status must be a boolean');
  }
  
  assertISOTimestamp(hold.created_at, 'Created at');
  assertISOTimestamp(hold.updated_at, 'Updated at');
}

// List Response Assertion
export function assertListResponse<T>(
  response: any,
  itemAssertion: (item: any) => asserts item is T
): void {
  if (!response || typeof response !== 'object') {
    throw new Error('List response must be an object');
  }

  if (!Array.isArray(response.items)) {
    throw new Error('List response items must be an array');
  }

  response.items.forEach((item: any, index: number) => {
    try {
      itemAssertion(item);
    } catch (error: any) {
      throw new Error(`Item at index ${index}: ${error.message}`);
    }
  });

  if (typeof response.total !== 'number' || response.total < 0) {
    throw new Error('Total must be non-negative');
  }

  if (typeof response.page !== 'number' || response.page < 1) {
    throw new Error('Page must be positive');
  }

  if (typeof response.per_page !== 'number' || response.per_page < 1) {
    throw new Error('Per page must be positive');
  }

  if (typeof response.has_next !== 'boolean') {
    throw new Error('Has next must be a boolean');
  }
}