import { ChildProcess, spawn } from 'child_process';
import { URL } from 'url';
import axios from 'axios';
import { killPort } from '../setup'; // Assumes killPort is exported from setup.ts

// Store the API process globally for access by start/stop
// Note: This assumes Jest runs tests serially (`--runInBand` or `maxWorkers=1`)
// If running in parallel, this approach needs refinement (e.g., unique ports per worker)
let apiProcess: ChildProcess | null = null;

/**
 * Starts the API server process based on environment variables.
 * It spawns the command defined in `API_START_COMMAND`, potentially changing
 * the working directory if specified (e.g., "cd ../implementation && npm run start").
 * It then polls the `API_URL` until the server responds or the timeout is reached.
 *
 * @param {number} [timeoutMs=30000] - Maximum time in milliseconds to wait for the server to start.
 * @returns {Promise<ChildProcess | null>} A promise resolving with the server process, or null if using mock mode.
 * @throws {Error} If `API_START_COMMAND` is not set, the process fails to spawn, or polling times out.
 */
export const startApiServer = async (timeoutMs: number = 30000): Promise<ChildProcess | null> => {
  if (apiProcess) {
    console.warn("API server process already exists. Skipping start.");
    return apiProcess;
  }

  const serverUrl = process.env.API_URL || 'http://localhost:3000';
  const port = new URL(serverUrl).port || '3000';

  // Ensure the port is free before attempting to start the server
  await killPort(port);

  return new Promise<ChildProcess | null>((resolve, reject) => {
    const apiStartCommand = process.env.API_START_COMMAND;
    if (!apiStartCommand) {
      console.error('API_START_COMMAND environment variable is not set.');
      return reject(new Error('API_START_COMMAND is not set. Cannot start API server.'));
    }

    // Parse the command to extract directory and actual command if 'cd' is used
    let directory = '.';
    let command = apiStartCommand;
    const cdMatch = apiStartCommand.match(/cd\s+([^\s&]+)\s*&&\s*(.*)/);
    if (cdMatch && cdMatch.length === 3) {
      directory = cdMatch[1];
      command = cdMatch[2];
      console.log(`Detected 'cd' in API_START_COMMAND. Will run command in directory: ${directory}`);
    }
    // console.log(`Attempting to start API server with command: "${command}" in directory: ${directory}`);

    // Split the command into executable and arguments
    const cmdParts = command.trim().split(/\s+/);
    const executable = cmdParts[0];
    const args = cmdParts.slice(1);

    // Spawn the server process
    try {
      apiProcess = spawn(executable, args, {
        cwd: directory,
        stdio: 'ignore', // Discard stdout/stderr to prevent buffer issues. Can be changed for debugging.
        detached: false, // Keep child process attached unless explicitly detached
      });
    } catch (spawnError: any) {
      console.error(`Failed to spawn command "${executable}" with args "${args.join(' ')}" in "${directory}":`, spawnError);
      apiProcess = null;
      return reject(spawnError);
    }


    const pollInterval = 800; // Poll every 800ms
    let timeElapsed = 0;
    let pollTimer: NodeJS.Timeout | null = null;
    let processExitedPrematurely = false;

    // Function to poll the server for readiness
    const pollServer = async () => {
      if (processExitedPrematurely || !apiProcess) {
        // Stop polling if the process exited unexpectedly or was stopped
        return;
      }

      if (timeElapsed > timeoutMs) {
        // console.error(`Polling timeout: API server at ${serverUrl} did not respond within ${timeoutMs}ms.`);
        apiProcess?.kill('SIGTERM'); // Attempt graceful shutdown
        apiProcess = null;
        return reject(new Error(`Timed out waiting for API server at ${serverUrl} to respond after ${timeoutMs}ms.`));
      }

      try {
        // Ping the base URL. A successful connection (even if it's a 404) indicates the server is up.
        await axios.get(serverUrl, {
          timeout: pollInterval - 100, // Short timeout for each poll attempt
          validateStatus: () => true, // Accept any status code as success for polling
        });
        // console.log(`API server responded at ${serverUrl}. Ready.`);
        // Optional small delay after connection confirmed, sometimes helps ensure routes are fully ready
        await sleep(100);
        resolve(apiProcess); // Server responded, resolve the promise
      } catch (error: any) {
        if (axios.isAxiosError(error) && error.code === 'ECONNREFUSED') {
          // Server not ready yet (connection refused), wait and retry
          // console.log(`Polling server at ${serverUrl}... (Connection refused)`); // Verbose logging
          timeElapsed += pollInterval;
          pollTimer = setTimeout(pollServer, pollInterval);
        } else {
          // Different error (network issue, config error?), reject
          console.error(`Error polling server at ${serverUrl}:`, error.message);
          apiProcess?.kill('SIGTERM');
          apiProcess = null;
          reject(new Error(`Polling failed for ${serverUrl}: ${error.message}`));
        }
      }
    };

    // Handle premature exit or errors during spawning
    apiProcess.on('error', (err) => {
      console.error('Failed to start API server process (spawn error):', err);
      processExitedPrematurely = true;
      if (pollTimer) clearTimeout(pollTimer);
      apiProcess = null;
      reject(err);
    });

    apiProcess.on('exit', (code, signal) => {
      // Only reject if the exit was unexpected (i.e., not initiated by stopApiServer)
      // and wasn't a clean exit (code 0 or null signal means clean exit or SIGTERM/SIGINT)
      if (apiProcess && code !== 0 && code !== null) {
        const message = `API server process exited prematurely with code ${code}, signal ${signal}.`;
        console.error(message);
        processExitedPrematurely = true;
        if (pollTimer) clearTimeout(pollTimer);
        apiProcess = null; // Ensure apiProcess is nullified
        // Reject immediately instead of waiting for poll timeout
        reject(new Error(message));
      } else {
         // Process exited cleanly or was stopped intentionally, ensure reference is cleared
         apiProcess = null;
      }
    });

    // Start polling after a short delay to allow the process to potentially start up
    // console.log(`Polling ${serverUrl} for readiness (timeout: ${timeoutMs}ms)...`);
    pollTimer = setTimeout(pollServer, pollInterval);
  });
};

/**
 * Stops the running API server process gracefully using SIGTERM.
 */
export const stopApiServer = (): void => {
  if (apiProcess) {
    // console.log(`Stopping API server process (PID: ${apiProcess.pid})...`);
    // Use SIGTERM for graceful shutdown, allowing the server to clean up
    const killed = apiProcess.kill('SIGTERM');
    if (killed) {
      // console.log('Sent SIGTERM to API server process.');
    } else {
      console.warn('Failed to send SIGTERM to API server process. It might have already exited.');
    }
    apiProcess = null; // Clear the reference immediately
  } else {
    // console.log("No active API server process found to stop."); // Less verbose
  }
};

/**
 * Wait for a specified amount of time
 * Useful for ensuring timestamp changes between operations
 *
 * @param ms Time to wait in milliseconds (default 1100ms)
 */
export const sleep = (ms: number = 1100): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};