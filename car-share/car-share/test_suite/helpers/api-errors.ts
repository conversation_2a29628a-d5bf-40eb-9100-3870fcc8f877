// Car-Share API Error Codes and HTTP Status Mappings

export enum ApiErrorId {
  // General Errors (400)
  ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER = 'ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER',
  ERR_MISSING_QUOTE_ID = 'ERR_MISSING_QUOTE_ID',
  
  // Authorization/Access Errors (403)
  ERR_FORBIDDEN = 'ERR_FORBIDDEN',
  ERR_USER_SUSPENDED = 'ERR_USER_SUSPENDED',
  ERR_LUXURY_LOCK = 'ERR_LUXURY_LOCK',
  ERR_REPUTATION_LOW = 'ERR_REPUTATION_LOW',
  ERR_PROBATIONARY_RESTRICTION_HIGH_ALERT = 'ERR_PROBATIONARY_RESTRICTION_HIGH_ALERT',
  
  // Resource Not Found Errors (404)
  ERR_ZONE_NOT_FOUND = 'ERR_ZONE_NOT_FOUND',
  
  // Conflict Errors (409)
  ERR_ACTIVE_RENTAL_EXISTS = 'ERR_ACTIVE_RENTAL_EXISTS',
  ERR_QUOTE_EXISTS = 'ERR_QUOTE_EXISTS',
  ERR_VEHICLE_NOT_RENTABLE = 'ERR_VEHICLE_NOT_RENTABLE',
  ERR_VEHICLE_ZONE_MISMATCH = 'ERR_VEHICLE_ZONE_MISMATCH',
  ERR_ZONE_LOCKED_FOR_RENTAL = 'ERR_ZONE_LOCKED_FOR_RENTAL',
  ERR_QUOTE_TAMPER = 'ERR_QUOTE_TAMPER',
  ERR_RENTAL_NOT_CONFIRMED = 'ERR_RENTAL_NOT_CONFIRMED',
  ERR_RENTAL_NOT_CANCELLABLE = 'ERR_RENTAL_NOT_CANCELLABLE',
  ERR_RENTAL_NOT_ACTIVE = 'ERR_RENTAL_NOT_ACTIVE',
  ERR_ZONE_LOCKED_FOR_RETURN = 'ERR_ZONE_LOCKED_FOR_RETURN',
  ERR_HOLD_NOT_ACTIVE = 'ERR_HOLD_NOT_ACTIVE',
  ERR_VEHICLE_NOT_IN_MAINTENANCE = 'ERR_VEHICLE_NOT_IN_MAINTENANCE',
  ERR_VEHICLE_NOT_DIRTY = 'ERR_VEHICLE_NOT_DIRTY',
  ERR_RENTAL_NOT_IN_REVIEW = 'ERR_RENTAL_NOT_IN_REVIEW',
  ERR_HOLD_NOT_OVERRIDABLE = 'ERR_HOLD_NOT_OVERRIDABLE',
  ERR_AMBIGUOUS_OVERRIDE_HOLD = 'ERR_AMBIGUOUS_OVERRIDE_HOLD',
  
  // Validation Errors (422)
  ERR_INVALID_ROLE = 'ERR_INVALID_ROLE',
  ERR_RESERVED_ZONE_CODE = 'ERR_RESERVED_ZONE_CODE',
  ERR_AMBIGUOUS_ZONE_UPDATE = 'ERR_AMBIGUOUS_ZONE_UPDATE',
  ERR_CANNOT_MODIFY_NON_RENTER = 'ERR_CANNOT_MODIFY_NON_RENTER',
  ERR_INVALID_UPDATE_FIELDS = 'ERR_INVALID_UPDATE_FIELDS',
  ERR_VEHICLE_CLASS_NOT_ALLOWED_IN_FLEET = 'ERR_VEHICLE_CLASS_NOT_ALLOWED_IN_FLEET',
  ERR_VEHICLE_MUST_HAVE_ZONE = 'ERR_VEHICLE_MUST_HAVE_ZONE',
  ERR_ODOMETER_ROLLBACK = 'ERR_ODOMETER_ROLLBACK',
  ERR_VEHICLE_NOT_EV = 'ERR_VEHICLE_NOT_EV',
  ERR_INSUFFICIENT_MAINTENANCE_OVERRIDE_MILES = 'ERR_INSUFFICIENT_MAINTENANCE_OVERRIDE_MILES'
}

// Error ID to HTTP Status Code Mapping
export const HTTP_STATUS_CODES: Record<ApiErrorId, number> = {
  // 400 Bad Request
  [ApiErrorId.ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER]: 400,
  [ApiErrorId.ERR_MISSING_QUOTE_ID]: 400,
  
  // 403 Forbidden
  [ApiErrorId.ERR_FORBIDDEN]: 403,
  [ApiErrorId.ERR_USER_SUSPENDED]: 403,
  [ApiErrorId.ERR_LUXURY_LOCK]: 403,
  [ApiErrorId.ERR_REPUTATION_LOW]: 403,
  [ApiErrorId.ERR_PROBATIONARY_RESTRICTION_HIGH_ALERT]: 403,
  
  // 404 Not Found
  [ApiErrorId.ERR_ZONE_NOT_FOUND]: 404,
  
  // 409 Conflict
  [ApiErrorId.ERR_ACTIVE_RENTAL_EXISTS]: 409,
  [ApiErrorId.ERR_QUOTE_EXISTS]: 409,
  [ApiErrorId.ERR_VEHICLE_NOT_RENTABLE]: 409,
  [ApiErrorId.ERR_VEHICLE_ZONE_MISMATCH]: 409,
  [ApiErrorId.ERR_ZONE_LOCKED_FOR_RENTAL]: 409,
  [ApiErrorId.ERR_QUOTE_TAMPER]: 409,
  [ApiErrorId.ERR_RENTAL_NOT_CONFIRMED]: 409,
  [ApiErrorId.ERR_RENTAL_NOT_CANCELLABLE]: 409,
  [ApiErrorId.ERR_RENTAL_NOT_ACTIVE]: 409,
  [ApiErrorId.ERR_ZONE_LOCKED_FOR_RETURN]: 409,
  [ApiErrorId.ERR_HOLD_NOT_ACTIVE]: 409,
  [ApiErrorId.ERR_VEHICLE_NOT_IN_MAINTENANCE]: 409,
  [ApiErrorId.ERR_VEHICLE_NOT_DIRTY]: 409,
  [ApiErrorId.ERR_RENTAL_NOT_IN_REVIEW]: 409,
  [ApiErrorId.ERR_HOLD_NOT_OVERRIDABLE]: 409,
  [ApiErrorId.ERR_AMBIGUOUS_OVERRIDE_HOLD]: 409,
  
  // 422 Unprocessable Entity
  [ApiErrorId.ERR_INVALID_ROLE]: 422,
  [ApiErrorId.ERR_RESERVED_ZONE_CODE]: 422,
  [ApiErrorId.ERR_AMBIGUOUS_ZONE_UPDATE]: 422,
  [ApiErrorId.ERR_CANNOT_MODIFY_NON_RENTER]: 422,
  [ApiErrorId.ERR_INVALID_UPDATE_FIELDS]: 422,
  [ApiErrorId.ERR_VEHICLE_CLASS_NOT_ALLOWED_IN_FLEET]: 422,
  [ApiErrorId.ERR_VEHICLE_MUST_HAVE_ZONE]: 422,
  [ApiErrorId.ERR_ODOMETER_ROLLBACK]: 422,
  [ApiErrorId.ERR_VEHICLE_NOT_EV]: 422,
  [ApiErrorId.ERR_INSUFFICIENT_MAINTENANCE_OVERRIDE_MILES]: 422
};

// Helper function to get status code for an error ID
export function getStatusCodeForError(errorId: ApiErrorId): number {
  return HTTP_STATUS_CODES[errorId] || 500;
}

// Type guard to check if a string is a valid ApiErrorId
export function isApiErrorId(value: string): value is ApiErrorId {
  return Object.values(ApiErrorId).includes(value as ApiErrorId);
}