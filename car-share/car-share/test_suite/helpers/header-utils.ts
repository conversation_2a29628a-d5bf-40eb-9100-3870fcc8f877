import { UserRole } from './types';
import { ApiHeaders } from './api-client';

export function buildUserHeaders(userId: string, userRole: UserRole = UserRole.RENTER): ApiHeaders {
  return {
    'X-User-ID': userId,
    'X-User-Role': userRole
  };
}

export function generateValidUserId(): string {
  const chars = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
  let id = '';
  for (let i = 0; i < 26; i++) {
    id += chars[Math.floor(Math.random() * chars.length)];
  }
  return `user_${id}`;
}