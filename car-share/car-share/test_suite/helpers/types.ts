// Car-Share API Types and Interfaces

// Standard API Response Envelope Types
export interface ApiMeta {
  api_request_id: string;
  api_request_timestamp: string;
  // Pagination fields
  limit?: number;
  offset?: number;
  total_count?: number;
  has_more?: boolean;
}

export interface ApiSuccessEnvelope<T> {
  meta: ApiMeta;
  response_type: 'object' | 'array';
  data: T;
}

export interface ErrorResponseData {
  error_id: string;
  message: string;
}

export interface ApiErrorEnvelope {
  meta: ApiMeta;
  response_type: 'error';
  data: ErrorResponseData;
}

export type ApiResponse<T> = ApiSuccessEnvelope<T> | ApiErrorEnvelope;

// Enumerations
export enum VehicleClass {
  ECONOMY = 'ECONOMY',
  STANDARD = 'STANDARD',
  LUXURY = 'LUXURY',
  EV = 'EV'
}

export enum VehicleState {
  AVAILABLE = 'AVAILABLE',
  RESERVED = 'RESERVED',
  IN_USE = 'IN_USE',
  NEEDS_CLEANING = 'NEEDS_CLEANING',
  NEEDS_MAINTENANCE = 'NEEDS_MAINTENANCE',
  RETIRED = 'RETIRED'
}

export enum UserRole {
  RENTER = 'RENTER',
  FLEET_OPERATOR = 'FLEET_OPERATOR',
  MECHANIC = 'MECHANIC'
}

export enum RentalState {
  CONFIRMED = 'CONFIRMED',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  INFRACTION_REVIEW = 'INFRACTION_REVIEW'
}

export enum ZoneLockState {
  OPEN = 'OPEN',
  SOFT_LOCK = 'SOFT_LOCK',
  HARD_LOCK = 'HARD_LOCK'
}

export enum FleetPhaseState {
  NORMAL = 'NORMAL',
  HIGH_ALERT = 'HIGH_ALERT'
}

export enum MaintenanceReasonCode {
  OIL_CHANGE = 'OIL_CHANGE',
  BRAKE_INSPECT = 'BRAKE_INSPECT',
  TIRE_ROTATION = 'TIRE_ROTATION',
  OTHER = 'OTHER'
}

export enum CleanlinessGrade {
  EXCELLENT = 'EXCELLENT',
  GOOD = 'GOOD',
  FAIR = 'FAIR',
  POOR = 'POOR'
}

export enum DamageLevel {
  NONE = 'NONE',
  MINOR = 'MINOR',
  MAJOR = 'MAJOR'
}

export enum ReportType {
  RETURN = 'RETURN',
  SPOT = 'SPOT',
  MAINT_CHECK = 'MAINT_CHECK'
}

export enum VehicleFlag {
  MAINT_HOLD = 'MAINT_HOLD',
  CLEAN_REQ = 'CLEAN_REQ',
  MAINT_OVERRIDE_GRANTED = 'MAINT_OVERRIDE_GRANTED'
}

// Resource Interfaces
export interface CityFleet {
  id: string;
  city_code: string;
  name: string;
  phase_state: FleetPhaseState;
  allowed_vehicle_classes: VehicleClass[];
  settings?: Record<string, any> | null;
  action_count?: number;
}

export interface ParkingZone {
  id: string;
  fleet_id: string;
  code: string;
  display_name: string;
  geojson: string;
  target_vehicles: number;
  ledger_surplus: number;
  lock_state: ZoneLockState;
  manual_lock: boolean;
  incentivized_classes?: VehicleClass[];
  discouraged_classes?: VehicleClass[];
}

export interface Vehicle {
  id: string;
  fleet_id: string;
  vin: string;
  license_plate: string;
  class: VehicleClass;
  state: VehicleState;
  odometer_mi: number;
  last_service_odometer_mi: number;
  service_interval_mi: number;
  current_zone_id: string | null;
  battery_pct?: number | null;
  flags: VehicleFlag[];
  miles_until_service: number;
  reputation_score?: number;
  luxury_unlock?: boolean;
  active_rental_id?: string | null;
  late_return_history?: string[];
  suspended?: boolean;
  status?: string;
}

export interface User {
  id: string;
  fleet_id: string;
  role: UserRole;
  name: string;
  email: string;
  phone?: string | null;
  reputation_score?: number;
  luxury_unlock?: boolean;
  active_rental_id?: string | null;
  late_return_history?: string[];
  suspended?: boolean;
  status?: string;
}

export interface Rental {
  id: string;
  renter_id: string;
  vehicle_id: string;
  state: RentalState;
  start_zone_id: string;
  end_zone_id: string | null;
  quoted_price_cents: number;
  distance_estimate_mi: number;
  start_odometer_mi?: number | null;
  end_odometer_mi?: number | null;
  start_action_count?: number | null;
  quoted_action_duration: number;
  surcharges_applied?: string[];
  discounts_applied?: string[];
  reputation_delta?: number;
  return_condition_report_id?: string | null;
  display_name: string;
  geojson: string;
  target_vehicles: number;
  vehicle_incentive_cents?: number;
  discouraged_classes?: VehicleClass[];
  incentivized_classes?: VehicleClass[];
}

export interface Quote {
  quote_id: string;
  price_cents: number;
  quoted_action_duration: number;
}

export interface ConditionReport {
  id: string;
  vehicle_id: string;
  inspector_id: string;
  report_type: ReportType;
  cleanliness_grade: CleanlinessGrade;
  damage_grade: DamageLevel;
  mileage_recorded: number;
  notes?: string | null;
}

export interface MaintenanceHold {
  id: string;
  fleet_id: string;
  vehicle_id: string;
  placed_by: string;
  reason_code: MaintenanceReasonCode;
  override_allowed: boolean;
  allowed_miles_remaining: number;
  active: boolean;
}

// Request Payload Interfaces
export interface CreateCityFleetPayload {
  city_code: string;
  name: string;
  allowed_vehicle_classes: VehicleClass[];
}

export interface CreateParkingZonePayload {
  code: string;
  display_name: string;
  geojson: string;
  target_vehicles: number;
  vehicle_incentive_cents?: number;
  discouraged_classes?: VehicleClass[];
  incentivized_classes?: VehicleClass[];
}

export interface UpdateParkingZonePayload {
  lock_state?: ZoneLockState;
  manual_lock?: boolean;
}

export interface CreateVehiclePayload {
  class: VehicleClass;
  vin: string;
  license_plate: string;
  service_interval_mi: number;
  odometer_mi: number;
  last_service_odometer_mi: number;
  current_zone_id: string;
  battery_pct?: number | null;
}

export interface CreateUserPayload {
  role: UserRole;
  name: string;
  email: string;
}

export interface UpdateUserPayload {
  suspended?: boolean;
  reputation_score?: number;
  late_return_history?: string[];
}

export interface CreateQuotePayload {
  vehicle_id: string;
  start_zone_id: string;
  end_zone_id: string;
  distance_estimate_mi: number;
}

export interface ConfirmRentalPayload {
  quote_id: string;
  quoted_price_cents: number;
}

export interface UnlockRentalPayload {
  action: 'unlock';
}

export interface ReturnRentalPayload {
  end_zone_id: string;
  end_odometer_mi: number;
  cleanliness_grade?: CleanlinessGrade;
  damage_grade?: DamageLevel;
}

export interface ReviewRentalPayload {
  resolution: 'approve' | 'waive_fee' | 'ban_user';
}

export interface CreateMaintenanceHoldPayload {
  vehicle_id: string;
  reason_code: MaintenanceReasonCode;
  override_allowed: boolean;
  allowed_miles_remaining: number;
}

export interface CreateConditionReportPayload {
  vehicle_id: string;
  report_type: ReportType;
  cleanliness_grade: CleanlinessGrade;
  damage_grade: DamageLevel;
  mileage_recorded: number;
  notes?: string;
}

export interface ServiceVehiclePayload {
  odometer_mi: number;
  service_notes?: string;
}

export interface ChargeVehiclePayload {
  battery_pct: number;
}

export interface CleanVehiclePayload {
  action: 'clean_complete';
}

// Ledger Types
export interface ZoneBalanceLedgerEntry {
  zone_id: string;
  surplus: number;
}

export interface VehicleMileageLedgerEntry {
  vehicle_id: string;
  miles_since_service: number;
  override_miles_credit: number;
}

export interface RenterReputationLedgerEntry {
  renter_id: string;
  reputation_score: number;
  late_counter: number;
}

// Fleet Summary Types
export interface FleetSummary {
  total_vehicles: number;
  available_vehicles: number;
  total_zones: number;
  locked_zones: number;
  active_rentals: number;
}

export interface RenterSummary {
  reputation_score: number;
  luxury_unlock: boolean;
  active_rental?: {
    id: string;
    vehicle_id: string;
    start_zone_id: string;
    state: RentalState;
  };
  rental_history_count: number;
}

// Capture types for test fixtures
export interface FixtureCaptures {
  fleetId?: string;
  vehicleId?: string;
  userId?: string;
  renterId?: string;
  operatorId?: string;
  mechanicId?: string;
  zoneId?: string;
  rentalId?: string;
  quoteId?: string;
  holdId?: string;
  reportId?: string;
  [key: string]: string | undefined;
}

// Test context types
export interface TestContext {
  fleetId: string;
  captures: FixtureCaptures;
  userId?: string;
}