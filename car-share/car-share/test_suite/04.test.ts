import { apiClient } from './helpers/api-client';
import { handleApiResponse, getSuccessData, validateErrorEnvelope } from './helpers/validators';
import { buildUserHeaders, generateValidUserId } from './helpers/header-utils';
import { fixtures, factories, derivations } from './helpers/fixtures';
import {
  UserRole,
  VehicleState,
  RentalState,
  ZoneLockState,
  VehicleClass, CleanlinessGrade,
  DamageLevel,
  TestContext,
  FleetPhaseState
} from './helpers/types';
import { ApiErrorId } from './helpers/api-errors';

describe('Test Suite 04 - Precedence, E2E, and Validation Tests', () => {
  let testContext: TestContext;

  beforeEach(async () => {
    testContext = {
      fleetId: '',
      captures: {}
    };

    const fleet = await fixtures.createTestFleet(testContext);
    testContext.fleetId = fleet.id;
  });

  describe('Hierarchy of Truth - Precedence Tests', () => {
    describe('PREC-1: Suspended user vs. vehicle not available', () => {
      it('should return ERR_USER_SUSPENDED before checking vehicle state', async () => {
        const suspendedRenter = await fixtures.setupSuspendedRenter(testContext);
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);

        const otherRenter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
        const otherRenterHeaders = buildUserHeaders(otherRenter.id, UserRole.RENTER);

        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, otherRenterHeaders);
        const quote = getSuccessData(quoteResponse);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, otherRenterHeaders);
        const rental = getSuccessData(rentalResponse);

        await apiClient.unlockRental(testContext.fleetId, rental.id, otherRenterHeaders);

        const suspendedHeaders = buildUserHeaders(suspendedRenter.id, UserRole.RENTER);
        const suspendedQuotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const response = await apiClient.createQuote(testContext.fleetId, suspendedQuotePayload, suspendedHeaders);

        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_USER_SUSPENDED });
      });
    });

    describe('PREC-2: Vehicle not available vs. zone locked', () => {
      it('should return ERR_VEHICLE_NOT_RENTABLE before checking zone lock', async () => {
        const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);

        const otherRenter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
        const otherRenterHeaders = buildUserHeaders(otherRenter.id, UserRole.RENTER);

        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, otherRenterHeaders);
        const quote = getSuccessData(quoteResponse);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, otherRenterHeaders);
        const rental = getSuccessData(rentalResponse);

        await apiClient.unlockRental(testContext.fleetId, rental.id, otherRenterHeaders);

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        await apiClient.updateParkingZone(
          testContext.fleetId,
          zone.id,
          { lock_state: ZoneLockState.HARD_LOCK },
          operatorHeaders
        );

        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
        const renterQuotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const response = await apiClient.createQuote(testContext.fleetId, renterQuotePayload, renterHeaders);

        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_RENTABLE });
      });
    });

    describe('PREC-3: Manual zone lock vs. rep too low', () => {
      it('should return ERR_ZONE_LOCKED_FOR_RENTAL before checking reputation', async () => {
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
        const lowRepRenter = await fixtures.setupLowRepRenter(testContext);

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        await apiClient.updateParkingZone(
          testContext.fleetId,
          zone.id,
          { lock_state: ZoneLockState.HARD_LOCK },
          operatorHeaders
        );

        const lowRepHeaders = buildUserHeaders(lowRepRenter.id, UserRole.RENTER);
        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const response = await apiClient.createQuote(testContext.fleetId, quotePayload, lowRepHeaders);

        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ZONE_LOCKED_FOR_RENTAL });
      });
    });

    describe('PREC-4: Hard lock return vs. odometer rollback', () => {
      it('should return ERR_ZONE_LOCKED_FOR_RETURN before checking odometer', async () => {
        const scenario = await fixtures.setupActiveRentalScenario(testContext);
        const destinationZone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        await apiClient.updateParkingZone(
          testContext.fleetId,
          destinationZone.id,
          { lock_state: ZoneLockState.HARD_LOCK },
          operatorHeaders
        );

        const renterHeaders = buildUserHeaders(scenario.renter.id, UserRole.RENTER);
        const returnPayload = factories.returnRentalPayload(
          destinationZone.id,
          scenario.rental.start_odometer_mi! - 10
        );
        const response = await apiClient.returnRental(
          testContext.fleetId,
          scenario.rental.id,
          returnPayload,
          renterHeaders
        );

        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ZONE_LOCKED_FOR_RETURN });
      });
    });

    describe('PREC-5: Odometer rollback vs. low reputation penalty', () => {
      it('should return ERR_ODOMETER_ROLLBACK before considering late return penalties', async () => {
        const scenario = await fixtures.setupActiveRentalScenario(testContext);

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        await apiClient.updateUser(
          testContext.fleetId,
          scenario.renter.id,
          { reputation_score: 70 },
          operatorHeaders
        );

        const renterHeaders = buildUserHeaders(scenario.renter.id, UserRole.RENTER);
        const returnPayload = factories.returnRentalPayload(
          scenario.zone.id,
          scenario.rental.start_odometer_mi! - 5
        );
        const response = await apiClient.returnRental(
          testContext.fleetId,
          scenario.rental.id,
          returnPayload,
          renterHeaders
        );

        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ODOMETER_ROLLBACK });
      });
    });

    describe('PREC-6: User suspension vs. luxury lock', () => {
      it('should return ERR_USER_SUSPENDED even for luxury vehicle access', async () => {
        const suspendedRenter = await fixtures.setupSuspendedRenter(testContext);

        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        await apiClient.updateUser(
          testContext.fleetId,
          suspendedRenter.id,
          { reputation_score: 200 },
          operatorHeaders
        );

        const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
        const luxuryVehicle = await fixtures.createTestVehicle(
          testContext.fleetId,
          zone.id,
          { class: VehicleClass.LUXURY },
          testContext
        );

        const suspendedHeaders = buildUserHeaders(suspendedRenter.id, UserRole.RENTER);
        const quotePayload = factories.quotePayload(luxuryVehicle.id, zone.id, zone.id);
        const response = await apiClient.createQuote(testContext.fleetId, quotePayload, suspendedHeaders);

        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_USER_SUSPENDED });
      });
    });
  });

  describe('End-to-End Tests', () => {
    describe('E2E-1: Standard rental happy path', () => {
      it('should complete a successful rental with all state transitions', async () => {
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
        const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id, { distance_estimate_mi: 15 });
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
        const quote = handleApiResponse(quoteResponse, 201);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
        const rental = handleApiResponse(rentalResponse, 201);

        expect(rental.state).toBe(RentalState.CONFIRMED);

        let vehicleCheck = await apiClient.getVehicle(testContext.fleetId, vehicle.id, operatorHeaders);
        expect(getSuccessData(vehicleCheck).state).toBe(VehicleState.RESERVED);

        const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
        const activeRental = handleApiResponse(unlockResponse, 200);

        expect(activeRental.state).toBe(RentalState.ACTIVE);
        expect(activeRental.start_odometer_mi).toBeDefined();
        expect(activeRental.start_action_count).toBeDefined();

        vehicleCheck = await apiClient.getVehicle(testContext.fleetId, vehicle.id, operatorHeaders);
        const vehicleInUse = getSuccessData(vehicleCheck);
        expect(vehicleInUse.state).toBe(VehicleState.IN_USE);
        expect(vehicleInUse.current_zone_id).toBeNull();

        const returnPayload = {
          end_zone_id: zone.id,
          end_odometer_mi: activeRental.start_odometer_mi! + 15,
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.NONE
        };
        const returnResponse = await apiClient.returnRental(
          testContext.fleetId,
          rental.id,
          returnPayload,
          renterHeaders
        );
        const completedRental = handleApiResponse(returnResponse, 200);

        expect(completedRental.state).toBe(RentalState.COMPLETED);

        vehicleCheck = await apiClient.getVehicle(testContext.fleetId, vehicle.id, operatorHeaders);
        const finalVehicle = getSuccessData(vehicleCheck);
        expect(finalVehicle.state).toBe(VehicleState.AVAILABLE);
        expect(finalVehicle.current_zone_id).toBe(zone.id);

        const finalRenter = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
        expect(getSuccessData(finalRenter).active_rental_id).toBeNull();
      });
    });

    describe('E2E-3: Maintenance override rental flow', () => {
      it('should handle maintenance override rental correctly', async () => {
        const { zone, vehicle, hold } = await fixtures.setupMaintenanceOverrideVehicle(testContext);
        const highRepRenter = await fixtures.setupHighRepRenter(testContext);

        const renterHeaders = buildUserHeaders(highRepRenter.id, UserRole.RENTER);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        let vehicleCheck = await apiClient.getVehicle(testContext.fleetId, vehicle.id, operatorHeaders);
        expect(getSuccessData(vehicleCheck).state).toBe(VehicleState.NEEDS_MAINTENANCE);

        const quotePayload = factories.quotePayload(
          vehicle.id,
          zone.id,
          zone.id,
          { distance_estimate_mi: 30 }
        );
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
        const quote = handleApiResponse(quoteResponse, 201);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
        const rental = handleApiResponse(rentalResponse, 201);

        expect(rental.state).toBe(RentalState.CONFIRMED);

        vehicleCheck = await apiClient.getVehicle(testContext.fleetId, vehicle.id, operatorHeaders);
        expect(getSuccessData(vehicleCheck).state).toBe(VehicleState.RESERVED);

        const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
        const activeRental = getSuccessData(unlockResponse);

        expect(activeRental.state).toBe(RentalState.ACTIVE);

        const returnPayload = {
          end_zone_id: zone.id,
          end_odometer_mi: activeRental.start_odometer_mi! + 30,
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.NONE
        };
        const returnResponse = await apiClient.returnRental(
          testContext.fleetId,
          rental.id,
          returnPayload,
          renterHeaders
        );
        const completedRental = handleApiResponse(returnResponse, 200);

        expect(completedRental.state).toBe(RentalState.COMPLETED);

        vehicleCheck = await apiClient.getVehicle(testContext.fleetId, vehicle.id, operatorHeaders);
        const finalVehicle = getSuccessData(vehicleCheck);
        expect(finalVehicle.state).toBe(VehicleState.NEEDS_MAINTENANCE);

        const holdCheck = await apiClient.getMaintenanceHold(testContext.fleetId, hold.id, operatorHeaders);
        const finalHold = getSuccessData(holdCheck);
        expect(finalHold.active).toBe(false);
      });
    });

    describe('E2E-4: Infraction scenario with damage', () => {
      it('should handle return with major damage and infraction review', async () => {
        const scenario = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(scenario.renter.id, UserRole.RENTER);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const returnPayload = {
          end_zone_id: scenario.zone.id,
          end_odometer_mi: scenario.rental.start_odometer_mi! + 10,
          cleanliness_grade: CleanlinessGrade.FAIR,
          damage_grade: DamageLevel.MAJOR
        };

        const returnResponse = await apiClient.returnRental(
          testContext.fleetId,
          scenario.rental.id,
          returnPayload,
          renterHeaders
        );
        const returnedRental = getSuccessData(returnResponse);

        const vehicleCheck = await apiClient.getVehicle(testContext.fleetId, scenario.vehicle.id, operatorHeaders);
        const damagedVehicle = getSuccessData(vehicleCheck);
        expect(damagedVehicle.state).toBe(VehicleState.NEEDS_MAINTENANCE);

        expect(returnedRental.state).toBe(RentalState.INFRACTION_REVIEW);

        const reviewPayload = { resolution: 'approve' as const };
        const reviewResponse = await apiClient.reviewRental(
          testContext.fleetId,
          scenario.rental.id,
          reviewPayload,
          operatorHeaders
        );
        const finalRental = handleApiResponse(reviewResponse, 200);

        expect(finalRental.state).toBe(RentalState.COMPLETED);
      });
    });

    describe('E2E-5: Cross-role interactions and cleanup', () => {
      it('should handle complex multi-role scenario with zone locks', async () => {
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        const newZone = await fixtures.createTestZone(
          testContext.fleetId,
          { target_vehicles: 2 },
          testContext
        );

        const newVehicle = await fixtures.createTestVehicle(
          testContext.fleetId,
          newZone.id,
          undefined,
          testContext
        );

        const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

        const quotePayload = factories.quotePayload(newVehicle.id, newZone.id, newZone.id);
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
        const quote = getSuccessData(quoteResponse);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
        const rental = getSuccessData(rentalResponse);

        const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
        const activeRental = getSuccessData(unlockResponse);


        await apiClient.updateParkingZone(
          testContext.fleetId,
          newZone.id,
          { lock_state: ZoneLockState.HARD_LOCK },
          operatorHeaders
        );

        const returnToLockedPayload = {
          end_zone_id: newZone.id,
          end_odometer_mi: activeRental.start_odometer_mi! + 10,
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.NONE
        };

        const failedReturnResponse = await apiClient.returnRental(
          testContext.fleetId,
          rental.id,
          returnToLockedPayload,
          renterHeaders
        );

        validateErrorEnvelope(failedReturnResponse, { expectedErrorId: ApiErrorId.ERR_ZONE_LOCKED_FOR_RETURN });

        const alternativeZone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);

        const successfulReturnPayload = {
          end_zone_id: alternativeZone.id,
          end_odometer_mi: activeRental.start_odometer_mi! + 10,
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.NONE
        };

        const successfulReturnResponse = await apiClient.returnRental(
          testContext.fleetId,
          rental.id,
          successfulReturnPayload,
          renterHeaders
        );

        const completedRental = handleApiResponse(successfulReturnResponse, 200);
        expect(completedRental.state).toBe(RentalState.COMPLETED);

        const finalVehicle = await apiClient.getVehicle(testContext.fleetId, newVehicle.id, operatorHeaders);
        expect(getSuccessData(finalVehicle).current_zone_id).toBe(alternativeZone.id);
      });
    });
  });

  describe('Validation Tests', () => {
    describe('VAL-1: Reject invalid City Code format', () => {
      it('should reject city code with numbers', async () => {
        const operatorHeaders = buildUserHeaders(generateValidUserId(), UserRole.FLEET_OPERATOR);
        const invalidFleetPayload = factories.cityFleetPayload({ city_code: 'SF4' });

        const response = await apiClient.createCityFleet(invalidFleetPayload, operatorHeaders);

        expect(response.status).toBe(422);
      });
    });

    describe('VAL-2: Reject creating user with too short name', () => {
      it('should reject user with name too short', async () => {
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        const invalidUserPayload = factories.userPayload(UserRole.RENTER, { name: 'A' });

        const response = await apiClient.createUser(testContext.fleetId, invalidUserPayload, operatorHeaders);

        expect(response.status).toBe(422);
      });
    });

    describe('VAL-3: Reject user with invalid email format', () => {
      it('should reject malformed email', async () => {
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        const invalidUserPayload = factories.userPayload(UserRole.RENTER, { email: 'not-an-email' });

        const response = await apiClient.createUser(testContext.fleetId, invalidUserPayload, operatorHeaders);

        expect(response.status).toBe(422);
      });
    });

    describe('VAL-4: Reject license_plate with invalid characters', () => {
      it('should reject license plate with invalid characters', async () => {
        const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
        const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);
        const invalidVehiclePayload = factories.vehiclePayload({
          current_zone_id: zone.id,
          license_plate: '@@123'
        });

        const response = await apiClient.createVehicle(testContext.fleetId, invalidVehiclePayload, mechanicHeaders);

        expect(response.status).toBe(422);
      });
    });

    describe('VAL-5: Reject VIN with incorrect length', () => {
      it('should reject VIN that is too short', async () => {
        const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
        const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);
        const invalidVehiclePayload = factories.vehiclePayload({
          current_zone_id: zone.id,
          vin: '12345'
        });

        const response = await apiClient.createVehicle(testContext.fleetId, invalidVehiclePayload, mechanicHeaders);

        expect(response.status).toBe(422);
      });
    });

    describe('VAL-6: Battery percentage required for EV', () => {
      it('should reject EV without battery_pct', async () => {
        const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
        const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);
        const invalidEVPayload = factories.vehiclePayload({
          current_zone_id: zone.id,
          class: VehicleClass.EV,
          battery_pct: undefined
        });

        const response = await apiClient.createVehicle(testContext.fleetId, invalidEVPayload, mechanicHeaders);

        expect(response.status).toBe(422);
      });
    });

    describe('VAL-7: Prevent duplicate zone code', () => {
      it('should reject duplicate zone codes', async () => {
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        const zoneCode = 'ABC-01';

        const firstZonePayload = factories.parkingZonePayload({ code: zoneCode });
        await apiClient.createParkingZone(testContext.fleetId, firstZonePayload, operatorHeaders);

        const duplicateZonePayload = factories.parkingZonePayload({ code: zoneCode });
        const response = await apiClient.createParkingZone(testContext.fleetId, duplicateZonePayload, operatorHeaders);

        expect(response.status).toBe(409);
      });
    });

    describe('VAL-8: Reserved zone code prefix "EA-" is blocked', () => {
      it('should reject zone codes starting with EA-', async () => {
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
        const reservedZonePayload = factories.parkingZonePayload({ code: 'EA-10' });

        const response = await apiClient.createParkingZone(testContext.fleetId, reservedZonePayload, operatorHeaders);

        validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_RESERVED_ZONE_CODE });
      });
    });
  });
  it('MZL-2: Zone returns to automatic control after manual_lock released', async () => {
    const zone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 10 }, testContext);
    const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

    await fixtures.createTestVehicle(testContext.fleetId, zone.id, undefined, testContext);
    await fixtures.createTestVehicle(testContext.fleetId, zone.id, undefined, testContext);

    await apiClient.updateParkingZone(
      testContext.fleetId,
      zone.id,
      { lock_state: ZoneLockState.OPEN },
      operatorHeaders
    );

    const releaseResponse = await apiClient.updateParkingZone(
      testContext.fleetId,
      zone.id,
      { manual_lock: false },
      operatorHeaders
    );

    const updatedZone = getSuccessData(releaseResponse);
    expect(updatedZone.lock_state).toBe(ZoneLockState.SOFT_LOCK);
    expect(updatedZone.manual_lock).toBe(false);
  });

  it('XFL-1: Cannot modify resources across fleet boundaries', async () => {
    const fleetAOperatorId = generateValidUserId();
    const fleetBOperatorId = generateValidUserId();

    const fleetAHeaders = buildUserHeaders(fleetAOperatorId, UserRole.FLEET_OPERATOR);
    const fleetBHeaders = buildUserHeaders(fleetBOperatorId, UserRole.FLEET_OPERATOR);

    const fleetAPayload = factories.cityFleetPayload();
    const fleetAResponse = await apiClient.createCityFleet(fleetAPayload, fleetAHeaders);
    const fleetA = getSuccessData(fleetAResponse);

    const fleetBPayload = factories.cityFleetPayload();
    const fleetBResponse = await apiClient.createCityFleet(fleetBPayload, fleetBHeaders);
    const fleetB = getSuccessData(fleetBResponse);

    const zoneA = await fixtures.createTestZone(fleetA.id, undefined, { fleetId: fleetA.id, captures: { operatorId: fleetAOperatorId } });
    const vehicleA = await fixtures.createTestVehicle(fleetA.id, zoneA.id, undefined, { fleetId: fleetA.id, captures: { operatorId: fleetAOperatorId } });

    const response = await apiClient.updateVehicleServiceStatus(
      fleetA.id,
      vehicleA.id,
      { action: 'clean_complete' },
      fleetBHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_FORBIDDEN });
  });

  describe('RBAC Field Redaction Tests', () => {
    it('RBAC-1: Renter gets full vehicle details as specified', async () => {
      const { vehicle } = await fixtures.setupMaintenanceOverrideVehicle(testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

      const response = await apiClient.getVehicle(testContext.fleetId, vehicle.id, renterHeaders);
      const vehicleData = handleApiResponse(response, 200);

      expect(vehicleData.id).toBeDefined();
      expect(vehicleData.fleet_id).toBeDefined();
      expect(vehicleData.vin).toBeDefined();
      expect(vehicleData.license_plate).toBeDefined();
      expect(vehicleData.class).toBeDefined();
      expect(vehicleData.state).toBeDefined();
      expect(vehicleData.odometer_mi).toBeDefined();
      expect(vehicleData.last_service_odometer_mi).toBeDefined();
      expect(vehicleData.service_interval_mi).toBeDefined();
      expect(vehicleData.miles_until_service).toBeDefined();
    });

    it('RBAC-2: Renter cannot access fleet-level ledgers', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

      const response = await apiClient.getZoneBalanceLedger(testContext.fleetId, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_FORBIDDEN });
    });

    it('RBAC-3: Mechanic cannot use Fleet Operator endpoints', async () => {
      const { vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
      const mechanic = await fixtures.createTestUser(testContext.fleetId, UserRole.MECHANIC, undefined, testContext);
      const mechanicHeaders = buildUserHeaders(mechanic.id, UserRole.MECHANIC);

      const response = await apiClient.retireVehicle(testContext.fleetId, vehicle.id, mechanicHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_FORBIDDEN });
    });
  });

  describe('Ledger Integrity Tests', () => {
    it('LEDG-1: Renter Reputation Ledger entry auto-created on user creation', async () => {
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      const userPayload = factories.userPayload(UserRole.RENTER);
      const response = await apiClient.createUser(testContext.fleetId, userPayload, operatorHeaders);
      const renter = getSuccessData(response);

      expect(renter.reputation_score).toBe(100);

      const ledgerResponse = await apiClient.getRenterReputationLedger(testContext.fleetId, renter.id, operatorHeaders);
      const ledgerEntry = getSuccessData(ledgerResponse);
      expect(ledgerEntry.reputation_score).toBe(100);
    });
  });
  describe('Edge Cases Tests', () => {
    it('EDGE-1: Zero-activity rental duration check', async () => {
      const { rental } = await fixtures.setupActiveRentalScenario(testContext);
      const renterHeaders = buildUserHeaders(testContext.captures.renterId!, UserRole.RENTER);

      const fleetBefore = await apiClient.getCityFleet(testContext.fleetId, renterHeaders);
      const initialFleet = getSuccessData(fleetBefore);

      const zone = await apiClient.getParkingZone(testContext.fleetId, testContext.captures.zoneId!, renterHeaders);
      const zoneData = getSuccessData(zone);
      const vehicle = await apiClient.getVehicle(testContext.fleetId, testContext.captures.vehicleId!, renterHeaders);
      const vehicleData = getSuccessData(vehicle);

      const returnPayload = factories.returnRentalPayload(zoneData.id, vehicleData.odometer_mi);
      const returnResponse = await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
      const returnedRental = getSuccessData(returnResponse);

      const fleetAfter = await apiClient.getCityFleet(testContext.fleetId, renterHeaders);
      const finalFleet = getSuccessData(fleetAfter);

      const isLate = derivations.rentalLatenessCheck(
        finalFleet.phase_state === FleetPhaseState.NORMAL ? 0 : 1,
        rental.start_action_count || 0,
        rental.quoted_action_duration
      );

      expect(isLate).toBe(false);
    });
  });

  describe('Zone Threshold Clarity Tests', () => {
    it('ZTHRESH-1: Zone enters SOFT_LOCK exactly at surplus -5', async () => {
      const zone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 10 }, testContext);

      for (let i = 0; i < 5; i++) {
        await fixtures.createTestVehicle(testContext.fleetId, zone.id, undefined, testContext);
      }

      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

      const vehicles = await apiClient.getVehicle(testContext.fleetId, testContext.captures.vehicleId!, renterHeaders);
      const vehicle = getSuccessData(vehicles);

      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
      const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
      const quote = getSuccessData(quoteResponse);

      const confirmPayload = {
        quote_id: quote.quote_id,
        quoted_price_cents: quote.price_cents
      };
      await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      const updatedZone = await apiClient.getParkingZone(testContext.fleetId, zone.id, operatorHeaders);
      const zoneData = getSuccessData(updatedZone);

      expect(zoneData.ledger_surplus).toBe(-5);
      expect(zoneData.lock_state).toBe(ZoneLockState.SOFT_LOCK);
    });

    it('ZTHRESH-2: Zone remains SOFT_LOCK at surplus -6', async () => {
      const zone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 10 }, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      await apiClient.updateParkingZone(testContext.fleetId, zone.id, { lock_state: ZoneLockState.SOFT_LOCK }, operatorHeaders);

      for (let i = 0; i < 4; i++) {
        await fixtures.createTestVehicle(testContext.fleetId, zone.id, undefined, testContext);
      }

      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

      const vehicle = await apiClient.getVehicle(testContext.fleetId, testContext.captures.vehicleId!, renterHeaders);
      const vehicleData = getSuccessData(vehicle);

      const quotePayload = factories.quotePayload(vehicleData.id, zone.id, zone.id);
      const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
      const quote = getSuccessData(quoteResponse);

      const confirmPayload = {
        quote_id: quote.quote_id,
        quoted_price_cents: quote.price_cents
      };
      await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);

      const updatedZone = await apiClient.getParkingZone(testContext.fleetId, zone.id, operatorHeaders);
      const zoneData = getSuccessData(updatedZone);

      expect(zoneData.ledger_surplus).toBe(-6);
      expect(zoneData.lock_state).toBe(ZoneLockState.SOFT_LOCK);
    });
  });

  describe('Emergency Management Tests', () => {
    it('EMG-1: Fleet phase escalation affects new quotes but not existing ones', async () => {
      const zone1 = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 1 }, testContext);
      const zone2 = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 1 }, testContext);
      const zone3 = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 1 }, testContext);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      await apiClient.updateParkingZone(testContext.fleetId, zone1.id, { lock_state: ZoneLockState.HARD_LOCK }, operatorHeaders);
      await apiClient.updateParkingZone(testContext.fleetId, zone2.id, { lock_state: ZoneLockState.HARD_LOCK }, operatorHeaders);

      const vehicle = await fixtures.createTestVehicle(testContext.fleetId, zone3.id, undefined, testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

      const quote1Payload = factories.quotePayload(vehicle.id, zone3.id, zone3.id);
      const quote1Response = await apiClient.createQuote(testContext.fleetId, quote1Payload, renterHeaders);
      const quote1 = getSuccessData(quote1Response);
      const price1 = quote1.price_cents;

      await apiClient.updateParkingZone(testContext.fleetId, zone3.id, { lock_state: ZoneLockState.HARD_LOCK }, operatorHeaders);

      const fleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const fleet = getSuccessData(fleetResponse);
      expect(fleet.phase_state).toBe(FleetPhaseState.HIGH_ALERT);

      const quote2Response = await apiClient.createQuote(testContext.fleetId, quote1Payload, renterHeaders);
      const quote2 = getSuccessData(quote2Response);
      const expectedSurchargedPrice = Math.round(price1 * 1.15);
      expect(quote2.price_cents).toBe(expectedSurchargedPrice);

      const confirmPayload = {
        quote_id: quote1.quote_id,
        quoted_price_cents: quote1.price_cents
      };
      const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
      handleApiResponse(rentalResponse, 201);
    });

    it('EMG-2: Manual lock release triggers automatic re-evaluation', async () => {
      const zone = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 10 }, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      await fixtures.createTestVehicle(testContext.fleetId, zone.id, undefined, testContext);
      await fixtures.createTestVehicle(testContext.fleetId, zone.id, undefined, testContext);

      await apiClient.updateParkingZone(testContext.fleetId, zone.id, { lock_state: ZoneLockState.HARD_LOCK }, operatorHeaders);

      const releaseResponse = await apiClient.updateParkingZone(
        testContext.fleetId,
        zone.id,
        { manual_lock: false },
        operatorHeaders
      );

      const updatedZone = getSuccessData(releaseResponse);
      expect(updatedZone.manual_lock).toBe(false);
      expect(updatedZone.lock_state).toBe(ZoneLockState.SOFT_LOCK);
    });

    it('EMG-3: Suspended renter can return vehicle but not start new rental', async () => {
      const { rental } = await fixtures.setupActiveRentalScenario(testContext);
      const renterId = testContext.captures.renterId!;
      const renterHeaders = buildUserHeaders(renterId, UserRole.RENTER);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      await apiClient.updateUser(testContext.fleetId, renterId, { suspended: true }, operatorHeaders);

      const zone = await apiClient.getParkingZone(testContext.fleetId, testContext.captures.zoneId!, renterHeaders);
      const zoneData = getSuccessData(zone);
      const vehicle = await apiClient.getVehicle(testContext.fleetId, testContext.captures.vehicleId!, renterHeaders);
      const vehicleData = getSuccessData(vehicle);

      const returnPayload = factories.returnRentalPayload(zoneData.id, vehicleData.odometer_mi + 10);
      const returnResponse = await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
      handleApiResponse(returnResponse, 200);

      const newVehicle = await fixtures.createTestVehicle(testContext.fleetId, zoneData.id, undefined, testContext);
      const quotePayload = factories.quotePayload(newVehicle.id, zoneData.id, zoneData.id);
      const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(quoteResponse, { expectedErrorId: ApiErrorId.ERR_USER_SUSPENDED });
    });

    it('EMG-5: Price validation uses stored hash not fresh pricing', async () => {
      const zone1 = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 1 }, testContext);
      const zone2 = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 1 }, testContext);
      const zone3 = await fixtures.createTestZone(testContext.fleetId, { target_vehicles: 1 }, testContext);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      // Set up two zones in HARD_LOCK
      await apiClient.updateParkingZone(testContext.fleetId, zone1.id, { lock_state: ZoneLockState.HARD_LOCK }, operatorHeaders);
      await apiClient.updateParkingZone(testContext.fleetId, zone2.id, { lock_state: ZoneLockState.HARD_LOCK }, operatorHeaders);

      const vehicle = await fixtures.createTestVehicle(testContext.fleetId, zone3.id, undefined, testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

      // Lock the third zone to trigger HIGH_ALERT
      await apiClient.updateParkingZone(testContext.fleetId, zone3.id, { lock_state: ZoneLockState.HARD_LOCK }, operatorHeaders);

      // Create a quote - should have 15% surcharge due to HIGH_ALERT
      const quotePayload = factories.quotePayload(vehicle.id, zone3.id, zone3.id);
      const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
      const quote = getSuccessData(quoteResponse);
      const surchargedPrice = quote.price_cents;

      // Fleet goes back to normal - fresh pricing would be lower
      await apiClient.updateParkingZone(testContext.fleetId, zone1.id, { lock_state: ZoneLockState.OPEN }, operatorHeaders);

      // Test 1: Confirm with the correct surcharged price should succeed
      // This proves the system uses the stored price hash, not fresh pricing
      const confirmPayload = {
        quote_id: quote.quote_id,
        quoted_price_cents: quote.price_cents
      };
      const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
      handleApiResponse(rentalResponse, 201);

      // Test 2: Create a new quote and try to tamper with the price
      const quote2Response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
      const quote2 = getSuccessData(quote2Response);

      // Attempt to confirm with a tampered (lower) price should fail
      const tamperedConfirmPayload = {
        quote_id: quote2.quote_id,
        quoted_price_cents: Math.round(quote2.price_cents * 0.85) // Try to pay 15% less
      };
      const tamperedResponse = await apiClient.confirmRental(testContext.fleetId, tamperedConfirmPayload, renterHeaders);
      validateErrorEnvelope(tamperedResponse, { expectedErrorId: ApiErrorId.ERR_QUOTE_TAMPER });
    });

    it('EMG-6: Active rental block persists after suspension lifted', async () => {
      const { rental } = await fixtures.setupActiveRentalScenario(testContext);
      const renterId = testContext.captures.renterId!;
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      const renterHeaders = buildUserHeaders(renterId, UserRole.RENTER);

      // This should fail since the rental is ACTIVE, not INFRACTION_REVIEW
      const reviewPayload = { resolution: 'approve' as const };
      const reviewResponse = await apiClient.reviewRental(testContext.fleetId, rental.id, reviewPayload, operatorHeaders);
      validateErrorEnvelope(reviewResponse, { expectedErrorId: ApiErrorId.ERR_RENTAL_NOT_IN_REVIEW });

      // The rest of the test should work without the review since active_rental_id is still set
      await apiClient.updateUser(testContext.fleetId, renterId, { suspended: true }, operatorHeaders);

      await apiClient.updateUser(testContext.fleetId, renterId, { suspended: false }, operatorHeaders);

      const newVehicle = await fixtures.createTestVehicle(testContext.fleetId, testContext.captures.zoneId!, undefined, testContext);
      const quotePayload = factories.quotePayload(newVehicle.id, testContext.captures.zoneId!, testContext.captures.zoneId!);
      const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(quoteResponse, { expectedErrorId: ApiErrorId.ERR_ACTIVE_RENTAL_EXISTS });
    });
  });
});