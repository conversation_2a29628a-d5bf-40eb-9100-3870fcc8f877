import { apiClient } from './helpers/api-client';
import { getSuccessData, validateErrorEnvelope } from './helpers/validators';
import { buildUserHeaders } from './helpers/header-utils';
import { fixtures, factories, derivations } from './helpers/fixtures';
import {
  TestContext,
  UserRole,
  VehicleClass,
  VehicleState,
  RentalState,
  FleetPhaseState,
  ZoneLockState,
  DamageLevel,
  ReportType,
  CleanlinessGrade
} from './helpers/types';
import { ApiErrorId } from './helpers/api-errors';

describe('Car Share API Advanced Features Test Suite', () => {
  let testContext: TestContext;

  beforeEach(async () => {
    testContext = {
      fleetId: '',
      captures: {}
    };

    const fleet = await fixtures.createTestFleet(testContext);
    testContext.fleetId = fleet.id;
  });

  describe('Probationary Renter Tests (PROB-)', () => {
    it('PROB-1: <PERSON><PERSON> enters PROBATIONARY status when reputation drops below 75', async () => {
      const { rental, renter } = await fixtures.setupActiveRentalScenario(testContext);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 78 },
        operatorHeaders
      );

      const fleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const fleet = getSuccessData(fleetResponse);
      const currentActionCount = fleet.action_count!;

      const rentalDuration = derivations.quotedActionDuration(rental.distance_estimate_mi);
      const lateActionCount = rental.start_action_count! + rentalDuration + 1;

      for (let i = currentActionCount; i <= lateActionCount; i++) {
        await apiClient.createUser(testContext.fleetId, factories.userPayload(UserRole.RENTER), operatorHeaders);
      }

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const returnResponse = await apiClient.returnRental(
        testContext.fleetId,
        rental.id,
        factories.returnRentalPayload(rental.start_zone_id, rental.start_odometer_mi! + 20),
        renterHeaders
      );

      const returnedRental = getSuccessData(returnResponse);

      const updatedRenterResponse = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
      const updatedRenter = getSuccessData(updatedRenterResponse);

      expect(updatedRenter.reputation_score).toBe(73);
      expect(updatedRenter.status).toBe('PROBATIONARY');
    });

    it('PROB-2: PROBATIONARY renter is blocked from renting during HIGH_ALERT', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 70 },
        operatorHeaders
      );

      await fixtures.setupHighAlertFleet(testContext);

      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );

      validateErrorEnvelope(quoteResponse, { expectedErrorId: ApiErrorId.ERR_PROBATIONARY_RESTRICTION_HIGH_ALERT });
    });

    it('PROB-3: Non-probationary renter is NOT blocked from renting during HIGH_ALERT', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 90 },
        operatorHeaders
      );

      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const baseQuoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );
      const baseQuote = getSuccessData(baseQuoteResponse);
      const basePrice = baseQuote.price_cents;

      await apiClient.deleteQuote(testContext.fleetId, baseQuote.quote_id, renterHeaders);

      await fixtures.setupHighAlertFleet(testContext);

      const highAlertQuoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );

      const highAlertQuote = getSuccessData(highAlertQuoteResponse);
      expect(highAlertQuote.price_cents).toBeGreaterThan(0);
      const expectedSurchargedPrice = Math.round(basePrice * 1.15);
      expect(highAlertQuote.price_cents).toBe(expectedSurchargedPrice);
    });

    it('PROB-4: PROBATIONARY renter gets a stricter rental duration quote', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 70 },
        operatorHeaders
      );

      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id, { distance_estimate_mi: 21 }),
        renterHeaders
      );

      const quote = getSuccessData(quoteResponse);
      expect(quote.quoted_action_duration).toBe(14);
    });

    it('PREC-7: Hierarchy: User suspension is checked before probationary restriction', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 70, suspended: true },
        operatorHeaders
      );

      await fixtures.setupHighAlertFleet(testContext);

      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );

      validateErrorEnvelope(quoteResponse, { expectedErrorId: ApiErrorId.ERR_USER_SUSPENDED });
    });

    it('E2E-6: Probationary renter\'s stricter duration causes a late return, deepening probation', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 72 },
        operatorHeaders
      );

      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id, { distance_estimate_mi: 25 }),
        renterHeaders
      );

      const quote = getSuccessData(quoteResponse);

      const confirmResponse = await apiClient.confirmRental(
        testContext.fleetId,
        {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        },
        renterHeaders
      );
      const rental = getSuccessData(confirmResponse);

      const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
      const activeRental = getSuccessData(unlockResponse);

      for (let i = 0; i < 15; i++) {
        await apiClient.createUser(testContext.fleetId, factories.userPayload(UserRole.RENTER), operatorHeaders);
      }

      const returnResponse = await apiClient.returnRental(
        testContext.fleetId,
        activeRental.id,
        factories.returnRentalPayload(zone.id, activeRental.start_odometer_mi! + 30),
        renterHeaders
      );

      const returnedRental = getSuccessData(returnResponse);

      expect(returnedRental.state).toBe(RentalState.INFRACTION_REVIEW);

      const updatedRenterResponse = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
      const updatedRenter = getSuccessData(updatedRenterResponse);

      expect(updatedRenter.reputation_score).toBe(67);
      expect(updatedRenter.status).toBe('PROBATIONARY');
    });
  });

  describe('Zone-Based Incentives Tests (ZINC-)', () => {
    it('ZINC-1: Quoting a discouraged vehicle class from a zone applies a 5% surcharge', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

      const normalZone = await fixtures.createTestZone(
        testContext.fleetId,
        {},
        testContext
      );
      const normalVehicle = await fixtures.createTestVehicle(
        testContext.fleetId,
        normalZone.id,
        { class: VehicleClass.ECONOMY },
        testContext
      );

      const baseQuoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(normalVehicle.id, normalZone.id, normalZone.id),
        renterHeaders
      );
      const baseQuote = getSuccessData(baseQuoteResponse);
      const basePrice = baseQuote.price_cents;

      await apiClient.deleteQuote(testContext.fleetId, baseQuote.quote_id, renterHeaders);

      const discourageZone = await fixtures.createTestZone(
        testContext.fleetId,
        { discouraged_classes: [VehicleClass.ECONOMY] },
        testContext
      );
      const discourageVehicle = await fixtures.createTestVehicle(
        testContext.fleetId,
        discourageZone.id,
        { class: VehicleClass.ECONOMY },
        testContext
      );

      const discourageQuoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(discourageVehicle.id, discourageZone.id, discourageZone.id),
        renterHeaders
      );

      const discourageQuote = getSuccessData(discourageQuoteResponse);
      const expectedSurchargedPrice = Math.round(basePrice * 1.05);
      expect(discourageQuote.price_cents).toBe(expectedSurchargedPrice);
    });

    it('ZINC-2: Returning an incentivized vehicle class to a zone grants a 5-point reputation bonus', async () => {
      const zone = await fixtures.createTestZone(
        testContext.fleetId,
        { incentivized_classes: [VehicleClass.LUXURY] },
        testContext
      );

      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 100 },
        operatorHeaders
      );

      const vehicle = await fixtures.createTestVehicle(
        testContext.fleetId,
        zone.id,
        { class: VehicleClass.LUXURY },
        testContext
      );

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );
      const quote = getSuccessData(quoteResponse);

      const confirmResponse = await apiClient.confirmRental(
        testContext.fleetId,
        {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        },
        renterHeaders
      );
      const rental = getSuccessData(confirmResponse);

      const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
      const activeRental = getSuccessData(unlockResponse);

      const returnResponse = await apiClient.returnRental(
        testContext.fleetId,
        activeRental.id,
        factories.returnRentalPayload(zone.id, activeRental.start_odometer_mi! + 15),
        renterHeaders
      );

      const returnedRental = getSuccessData(returnResponse);

      const updatedRenterResponse = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
      const updatedRenter = getSuccessData(updatedRenterResponse);

      expect(updatedRenter.reputation_score).toBe(105);
    });

    it('ZINC-3: Returning a discouraged vehicle class to a zone applies a 5-point reputation penalty', async () => {
      const zone = await fixtures.createTestZone(
        testContext.fleetId,
        { discouraged_classes: [VehicleClass.LUXURY] },
        testContext
      );

      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 100 },
        operatorHeaders
      );

      const vehicle = await fixtures.createTestVehicle(
        testContext.fleetId,
        zone.id,
        { class: VehicleClass.LUXURY },
        testContext
      );

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );
      const quote = getSuccessData(quoteResponse);

      const confirmResponse = await apiClient.confirmRental(
        testContext.fleetId,
        {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        },
        renterHeaders
      );
      const rental = getSuccessData(confirmResponse);

      const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
      const activeRental = getSuccessData(unlockResponse);

      const returnResponse = await apiClient.returnRental(
        testContext.fleetId,
        activeRental.id,
        factories.returnRentalPayload(zone.id, activeRental.start_odometer_mi! + 15),
        renterHeaders
      );

      const returnedRental = getSuccessData(returnResponse);

      const updatedRenterResponse = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
      const updatedRenter = getSuccessData(updatedRenterResponse);

      expect(updatedRenter.reputation_score).toBe(95);
    });

    it('ZINC-4: Cumulative Surcharges: HIGH_ALERT and Zone Discouragement are applied together', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

      const normalZone = await fixtures.createTestZone(
        testContext.fleetId,
        {},
        testContext
      );
      const normalVehicle = await fixtures.createTestVehicle(
        testContext.fleetId,
        normalZone.id,
        { class: VehicleClass.ECONOMY },
        testContext
      );

      const baseQuoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(normalVehicle.id, normalZone.id, normalZone.id),
        renterHeaders
      );
      const baseQuote = getSuccessData(baseQuoteResponse);
      const basePrice = baseQuote.price_cents;

      await apiClient.deleteQuote(testContext.fleetId, baseQuote.quote_id, renterHeaders);

      await fixtures.setupHighAlertFleet(testContext);

      const zone = await fixtures.createTestZone(
        testContext.fleetId,
        { discouraged_classes: [VehicleClass.ECONOMY] },
        testContext
      );
      const vehicle = await fixtures.createTestVehicle(
        testContext.fleetId,
        zone.id,
        { class: VehicleClass.ECONOMY },
        testContext
      );

      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );

      const quote = getSuccessData(quoteResponse);
      const expectedSurchargedPrice = Math.round(basePrice * 1.20);
      expect(quote.price_cents).toBe(expectedSurchargedPrice);
    });

    it('E2E-7: Renter escapes probationary status by using a zone incentive', async () => {
      const zone = await fixtures.createTestZone(
        testContext.fleetId,
        { incentivized_classes: [VehicleClass.STANDARD] },
        testContext
      );

      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 74 },
        operatorHeaders
      );

      const probationaryRenterResponse = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
      const probationaryRenter = getSuccessData(probationaryRenterResponse);
      expect(probationaryRenter.status).toBe('PROBATIONARY');

      const vehicle = await fixtures.createTestVehicle(
        testContext.fleetId,
        zone.id,
        { class: VehicleClass.STANDARD },
        testContext
      );

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );
      const quote = getSuccessData(quoteResponse);

      const confirmResponse = await apiClient.confirmRental(
        testContext.fleetId,
        {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        },
        renterHeaders
      );
      const rental = getSuccessData(confirmResponse);

      const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
      const activeRental = getSuccessData(unlockResponse);

      const returnResponse = await apiClient.returnRental(
        testContext.fleetId,
        activeRental.id,
        factories.returnRentalPayload(zone.id, activeRental.start_odometer_mi! + 15),
        renterHeaders
      );

      const returnedRental = getSuccessData(returnResponse);
      expect(returnedRental.state).toBe(RentalState.COMPLETED);

      const stillProbationaryResponse = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
      const stillProbationary = getSuccessData(stillProbationaryResponse);

      expect(stillProbationary.reputation_score).toBe(79);
      expect(stillProbationary.status).toBe('PROBATIONARY');

      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 81 },
        operatorHeaders
      );

      const vehicle2 = await fixtures.createTestVehicle(
        testContext.fleetId,
        zone.id,
        { class: VehicleClass.STANDARD },
        testContext
      );

      const quote2Response = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle2.id, zone.id, zone.id),
        renterHeaders
      );
      const quote2 = getSuccessData(quote2Response);

      const confirm2Response = await apiClient.confirmRental(
        testContext.fleetId,
        {
          quote_id: quote2.quote_id,
          quoted_price_cents: quote2.price_cents
        },
        renterHeaders
      );
      const rental2 = getSuccessData(confirm2Response);

      const unlock2Response = await apiClient.unlockRental(testContext.fleetId, rental2.id, renterHeaders);
      const activeRental2 = getSuccessData(unlock2Response);

      const return2Response = await apiClient.returnRental(
        testContext.fleetId,
        activeRental2.id,
        factories.returnRentalPayload(zone.id, activeRental2.start_odometer_mi! + 15),
        renterHeaders
      );

      const returnedRental2 = getSuccessData(return2Response);
      expect(returnedRental2.state).toBe(RentalState.COMPLETED);

      const updatedRenterResponse = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
      const updatedRenter = getSuccessData(updatedRenterResponse);

      expect(updatedRenter.reputation_score).toBe(86);
      expect(updatedRenter.status).toBe('NORMAL');
    });

    it('XRES-7: Late return penalty and zone incentive bonus are both applied', async () => {
      const zone = await fixtures.createTestZone(
        testContext.fleetId,
        { incentivized_classes: [VehicleClass.STANDARD] },
        testContext
      );

      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { reputation_score: 100 },
        operatorHeaders
      );

      const vehicle1 = await fixtures.createTestVehicle(
        testContext.fleetId,
        zone.id,
        { class: VehicleClass.STANDARD },
        testContext
      );

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quote1Response = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle1.id, zone.id, zone.id),
        renterHeaders
      );
      const quote1 = getSuccessData(quote1Response);

      const confirm1Response = await apiClient.confirmRental(
        testContext.fleetId,
        {
          quote_id: quote1.quote_id,
          quoted_price_cents: quote1.price_cents
        },
        renterHeaders
      );
      const rental1 = getSuccessData(confirm1Response);

      const unlock1Response = await apiClient.unlockRental(testContext.fleetId, rental1.id, renterHeaders);
      const activeRental1 = getSuccessData(unlock1Response);

      for (let i = 0; i < activeRental1.quoted_action_duration + 5; i++) {
        await apiClient.createUser(testContext.fleetId, factories.userPayload(UserRole.RENTER), operatorHeaders);
      }

      const return1Response = await apiClient.returnRental(
        testContext.fleetId,
        activeRental1.id,
        factories.returnRentalPayload(zone.id, activeRental1.start_odometer_mi! + 15),
        renterHeaders
      );

      const returnedRental1 = getSuccessData(return1Response);
      expect(returnedRental1.state).toBe(RentalState.INFRACTION_REVIEW);

      await apiClient.reviewRental(testContext.fleetId, rental1.id, { resolution: 'approve' }, operatorHeaders);


      const vehicle = await fixtures.createTestVehicle(
        testContext.fleetId,
        zone.id,
        { class: VehicleClass.STANDARD },
        testContext
      );

      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );
      const quote = getSuccessData(quoteResponse);

      const confirmResponse = await apiClient.confirmRental(
        testContext.fleetId,
        {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        },
        renterHeaders
      );
      const rental = getSuccessData(confirmResponse);

      const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
      const activeRental = getSuccessData(unlockResponse);

      for (let i = 0; i < activeRental.quoted_action_duration + 5; i++) {
        await apiClient.createUser(testContext.fleetId, factories.userPayload(UserRole.RENTER), operatorHeaders);
      }

      const returnResponse = await apiClient.returnRental(
        testContext.fleetId,
        activeRental.id,
        factories.returnRentalPayload(zone.id, activeRental.start_odometer_mi! + 15),
        renterHeaders
      );

      const returnedRental = getSuccessData(returnResponse);
      expect(returnedRental.state).toBe(RentalState.INFRACTION_REVIEW);

      const updatedRenterResponse = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
      const updatedRenter = getSuccessData(updatedRenterResponse);

      expect(updatedRenter.reputation_score).toBe(90);
    });
  });

  describe('Fleet Phase State Tests (PHASE-)', () => {
    it('PHASE-1: Fleet enters HIGH_ALERT when 3 zones reach HARD_LOCK', async () => {
      const zones = await Promise.all([
        fixtures.createTestZone(testContext.fleetId, { target_vehicles: 15 }, testContext),
        fixtures.createTestZone(testContext.fleetId, { target_vehicles: 15 }, testContext),
        fixtures.createTestZone(testContext.fleetId, { target_vehicles: 15 }, testContext),
      ]);

      for (const zone of zones) {
        for (let i = 0; i < 5; i++) {
          await fixtures.createTestVehicle(testContext.fleetId, zone.id, undefined, testContext);
        }
      }

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      for (const zone of zones) {
        const zoneResponse = await apiClient.getParkingZone(testContext.fleetId, zone.id, operatorHeaders);
        const updatedZone = getSuccessData(zoneResponse);
        expect(updatedZone.lock_state).toBe(ZoneLockState.HARD_LOCK);
      }

      const fleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const fleet = getSuccessData(fleetResponse);
      expect(fleet.phase_state).toBe(FleetPhaseState.HIGH_ALERT);
    });

    it('PHASE-2: Fleet returns to NORMAL when HARD_LOCK zones drop to 2', async () => {
      const zones = await fixtures.setupHighAlertFleet(testContext);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      await apiClient.updateParkingZone(
        testContext.fleetId,
        zones[0].id,
        { lock_state: ZoneLockState.OPEN },
        operatorHeaders
      );

      await apiClient.updateParkingZone(
        testContext.fleetId,
        zones[0].id,
        { manual_lock: false },
        operatorHeaders
      );

      const fleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const fleet = getSuccessData(fleetResponse);
      expect(fleet.phase_state).toBe(FleetPhaseState.NORMAL);
    });
  });

  describe('Fleet Action Counter Tests (FAC-)', () => {
    it('FAC-1: POST endpoints increment Fleet Action Counter', async () => {
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      const initialFleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const initialFleet = getSuccessData(initialFleetResponse);
      const initialCount = initialFleet.action_count!;

      const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
      await fixtures.createTestVehicle(testContext.fleetId, zone.id, undefined, testContext);

      const finalFleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const finalFleet = getSuccessData(finalFleetResponse);
      const finalCount = finalFleet.action_count!;

      expect(finalCount).toBe(initialCount + 2);
    });

    it('FAC-2: PATCH endpoints increment Fleet Action Counter', async () => {
      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      const renterHeaders = buildUserHeaders(testContext.captures.renterId!, UserRole.RENTER);

      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(vehicle.id, zone.id, zone.id),
        renterHeaders
      );
      const quote = getSuccessData(quoteResponse);

      const confirmResponse = await apiClient.confirmRental(
        testContext.fleetId,
        {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        },
        renterHeaders
      );
      const rental = getSuccessData(confirmResponse);

      const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
      const activeRental = getSuccessData(unlockResponse);

      const returnPayload = {
        ...factories.returnRentalPayload(zone.id, activeRental.start_odometer_mi! + 10),
        cleanliness_grade: CleanlinessGrade.POOR
      };

      await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);

      const dirtyVehicleResponse = await apiClient.getVehicle(testContext.fleetId, vehicle.id, operatorHeaders);
      const dirtyVehicle = getSuccessData(dirtyVehicleResponse);
      expect(dirtyVehicle.state).toBe(VehicleState.NEEDS_CLEANING);

      const initialFleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const initialFleet = getSuccessData(initialFleetResponse);
      const initialCount = initialFleet.action_count!;

      await apiClient.updateVehicleServiceStatus(
        testContext.fleetId,
        vehicle.id,
        { action: 'clean_complete' },
        operatorHeaders
      );

      const finalFleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const finalFleet = getSuccessData(finalFleetResponse);
      const finalCount = finalFleet.action_count!;

      expect(finalCount).toBe(initialCount + 1);
    });

    it('FAC-3: GET endpoints do NOT increment Fleet Action Counter', async () => {
      const { vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      const initialFleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const initialFleet = getSuccessData(initialFleetResponse);
      const initialCount = initialFleet.action_count!;

      await apiClient.getVehicle(testContext.fleetId, vehicle.id, operatorHeaders);

      const finalFleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const finalFleet = getSuccessData(finalFleetResponse);
      const finalCount = finalFleet.action_count!;

      expect(finalCount).toBe(initialCount);
    });

    it('FAC-4: Failed state-modifying requests do NOT increment counter', async () => {
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

      const initialFleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const initialFleet = getSuccessData(initialFleetResponse);
      const initialCount = initialFleet.action_count!;

      const mechanic = await fixtures.createTestUser(testContext.fleetId, UserRole.MECHANIC, undefined, testContext);
      const mechanicHeaders = buildUserHeaders(mechanic.id, UserRole.MECHANIC);

      const invalidVehicleResponse = await apiClient.createVehicle(
        testContext.fleetId,
        {
          class: VehicleClass.ECONOMY,
          vin: 'INVALID_VIN',
          license_plate: 'ABC123',
          service_interval_mi: 5000,
          odometer_mi: 1000,
          last_service_odometer_mi: 1000,
          current_zone_id: 'nonexistent_zone',
        },
        mechanicHeaders
      );

      expect(invalidVehicleResponse.status).toBe(422);

      const finalFleetResponse = await apiClient.getCityFleet(testContext.fleetId, operatorHeaders);
      const finalFleet = getSuccessData(finalFleetResponse);
      const finalCount = finalFleet.action_count!;

      expect(finalCount).toBe(initialCount);
    });
  });

  describe('Condition Reports Tests (CR-)', () => {
    it('CR-1: SPOT report with MINOR damage on available vehicle', async () => {
      const { vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);

      const reportResponse = await apiClient.createConditionReport(
        testContext.fleetId,
        {
          vehicle_id: vehicle.id,
          report_type: ReportType.SPOT,
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.MINOR,
          mileage_recorded: vehicle.odometer_mi + 10,
        },
        renterHeaders
      );

      const report = getSuccessData(reportResponse);
      expect(report.damage_grade).toBe(DamageLevel.MINOR);

      const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, vehicle.id, renterHeaders);
      const updatedVehicle = getSuccessData(vehicleResponse);
      expect(updatedVehicle.state).toBe(VehicleState.AVAILABLE);
    });

    it('CR-2: MAINT_CHECK report with MAJOR damage transitions vehicle', async () => {
      const { vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
      const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);

      const reportResponse = await apiClient.createConditionReport(
        testContext.fleetId,
        {
          vehicle_id: vehicle.id,
          report_type: ReportType.MAINT_CHECK,
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.MAJOR,
          mileage_recorded: vehicle.odometer_mi + 10,
        },
        mechanicHeaders
      );

      const report = getSuccessData(reportResponse);
      expect(report.damage_grade).toBe(DamageLevel.MAJOR);

      const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, vehicle.id, mechanicHeaders);
      const updatedVehicle = getSuccessData(vehicleResponse);
      expect(updatedVehicle.state).toBe(VehicleState.NEEDS_MAINTENANCE);
    });

    it('CR-3: SPOT report on RESERVED vehicle with MAJOR damage cancels rental', async () => {
      const { rental, vehicle, renter } = await fixtures.setupActiveRentalScenario(testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const { vehicle: reservedVehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);

      const quoteResponse = await apiClient.createQuote(
        testContext.fleetId,
        factories.quotePayload(reservedVehicle.id, zone.id, zone.id),
        renterHeaders
      );
      const quote = getSuccessData(quoteResponse);

      const confirmResponse = await apiClient.confirmRental(
        testContext.fleetId,
        {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        },
        renterHeaders
      );
      const confirmedRental = getSuccessData(confirmResponse);

      const vehicleResponse = await apiClient.getVehicle(testContext.fleetId, reservedVehicle.id, renterHeaders);
      const vehicleBeforeReport = getSuccessData(vehicleResponse);
      expect(vehicleBeforeReport.state).toBe(VehicleState.RESERVED);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      const reportResponse = await apiClient.createConditionReport(
        testContext.fleetId,
        {
          vehicle_id: reservedVehicle.id,
          report_type: ReportType.SPOT,
          cleanliness_grade: CleanlinessGrade.GOOD,
          damage_grade: DamageLevel.MAJOR,
          mileage_recorded: reservedVehicle.odometer_mi + 5,
        },
        operatorHeaders
      );

      const report = getSuccessData(reportResponse);

      const updatedVehicleResponse = await apiClient.getVehicle(testContext.fleetId, reservedVehicle.id, operatorHeaders);
      const updatedVehicle = getSuccessData(updatedVehicleResponse);
      expect(updatedVehicle.state).toBe(VehicleState.NEEDS_MAINTENANCE);

      const updatedRentalResponse = await apiClient.getRental(testContext.fleetId, confirmedRental.id, renterHeaders);
      const updatedRental = getSuccessData(updatedRentalResponse);
      expect(updatedRental.state).toBe(RentalState.CANCELLED);

      const updatedRenterResponse = await apiClient.getUser(testContext.fleetId, renter.id, operatorHeaders);
      const updatedRenter = getSuccessData(updatedRenterResponse);
      expect(updatedRenter.active_rental_id).toBeNull();
    });
  });

  describe('Battery Charging Tests (BATT-)', () => {
    it('BATT-1: Successful EV charging sets battery to 100%', async () => {
      const { zone } = await fixtures.setupBasicAvailableVehicle(testContext);
      const evVehicle = await fixtures.createTestVehicle(
        testContext.fleetId,
        zone.id,
        { class: VehicleClass.EV, battery_pct: 45 },
        testContext
      );

      const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);

      const chargeResponse = await apiClient.chargeVehicle(
        testContext.fleetId,
        evVehicle.id,
        { battery_pct: 100 },
        mechanicHeaders
      );

      const chargedVehicle = getSuccessData(chargeResponse);
      expect(chargedVehicle.battery_pct).toBe(100);
      expect(chargedVehicle.state).toBe(evVehicle.state);
    });
  });
});