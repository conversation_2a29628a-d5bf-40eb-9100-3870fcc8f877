import { apiClient } from './helpers/api-client';
import { getSuccessData, handleApiResponse, validateErrorEnvelope } from './helpers/validators';
import { buildUserHeaders, generateValidUserId } from './helpers/header-utils';
import { fixtures, factories } from './helpers/fixtures';
import {
  UserRole,
  VehicleClass, TestContext,
  VehicleState,
  RentalState,
  DamageLevel,
  ZoneLockState,
  MaintenanceReasonCode
} from './helpers/types';
import { ApiErrorId } from './helpers/api-errors';

describe('Business Rule Validation Tests', () => {
  let testContext: TestContext;

  beforeEach(async () => {
    testContext = {
      fleetId: '',
      captures: {}
    };

    const fleet = await fixtures.createTestFleet(testContext);
    testContext.fleetId = fleet.id;
  });

  describe('BR-1: Suspended user cannot initiate quote', () => {
    it('should reject quote creation by suspended user', async () => {
      const suspendedRenter = await fixtures.setupSuspendedRenter(testContext);
      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);

      const renterHeaders = buildUserHeaders(suspendedRenter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_USER_SUSPENDED });
    });
  });

  describe('BR-2: User with existing active rental cannot create a new quote', () => {
    it('should reject quote creation by user with active rental', async () => {
      const activeRentalSetup = await fixtures.setupActiveRentalScenario(testContext);
      const { vehicle: secondVehicle, zone: secondZone } = await fixtures.setupBasicAvailableVehicle(testContext);

      const renterHeaders = buildUserHeaders(activeRentalSetup.renter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(secondVehicle.id, secondZone.id, secondZone.id);
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ACTIVE_RENTAL_EXISTS });
    });
  });

  describe('BR-3: Prevent second active quote for same renter', () => {
    it('should reject duplicate quote attempt', async () => {
      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
      await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      const { vehicle: secondVehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
      const secondQuotePayload = factories.quotePayload(secondVehicle.id, zone.id, zone.id);
      const response = await apiClient.createQuote(testContext.fleetId, secondQuotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_QUOTE_EXISTS });
    });
  });

  describe('BR-4: Vehicle not rentable due to state', () => {
    it('should reject quote for IN_USE vehicle', async () => {
      const activeRentalSetup = await fixtures.setupActiveRentalScenario(testContext);
      const newRenter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterHeaders = buildUserHeaders(newRenter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(activeRentalSetup.vehicle.id, activeRentalSetup.zone.id, activeRentalSetup.zone.id);
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_RENTABLE });
    });
  });

  describe('BR-5: Vehicle not rentable due to low EV battery', () => {
    it('should reject quote for EV with battery < 20%', async () => {
      const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
      const lowBatteryEV = await fixtures.createTestVehicle(testContext.fleetId, zone.id, {
        class: VehicleClass.EV,
        battery_pct: 15
      }, testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(lowBatteryEV.id, zone.id, zone.id);
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_RENTABLE });
    });
  });

  describe('BR-6: Vehicle not rentable due to required maintenance', () => {
    it('should reject quote for vehicle due for service', async () => {
      const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
      const maintenanceVehicle = await fixtures.createTestVehicle(testContext.fleetId, zone.id, {
        odometer_mi: 10000,
        last_service_odometer_mi: 5000,
        service_interval_mi: 5000
      }, testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(maintenanceVehicle.id, zone.id, zone.id);
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_RENTABLE });
    });
  });

  describe('BR-7: Override rental blocked by insufficient rep', () => {
    it('should reject override attempt with low reputation', async () => {
      const { vehicle, zone } = await fixtures.setupMaintenanceOverrideVehicle(testContext);
      const lowRepRenter = await fixtures.setupLowRepRenter(testContext);

      const renterHeaders = buildUserHeaders(lowRepRenter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id, {
        distance_estimate_mi: 30
      });
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_REPUTATION_LOW });
    });
  });

  describe('BR-8: Override rental blocked by multiple holds', () => {
    it('should reject override attempt with multiple holds', async () => {
      const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
      const vehicle = await fixtures.createTestVehicle(testContext.fleetId, zone.id, {
        odometer_mi: 5000,
        last_service_odometer_mi: 0,
        service_interval_mi: 5000
      }, testContext);

      const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);

      const hold1Payload = factories.maintenanceHoldPayload(vehicle.id, {
        override_allowed: true,
        allowed_miles_remaining: 50
      });
      await apiClient.createMaintenanceHold(testContext.fleetId, hold1Payload, mechanicHeaders);

      const hold2Payload = factories.maintenanceHoldPayload(vehicle.id, {
        override_allowed: true,
        allowed_miles_remaining: 30
      });
      await apiClient.createMaintenanceHold(testContext.fleetId, hold2Payload, mechanicHeaders);

      const highRepRenter = await fixtures.setupHighRepRenter(testContext);

      const renterHeaders = buildUserHeaders(highRepRenter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_AMBIGUOUS_OVERRIDE_HOLD });
    });
  });

  describe('BR-9: Override rental blocked by non-overridable hold', () => {
    it('should reject override attempt with non-overridable hold', async () => {
      const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
      const vehicle = await fixtures.createTestVehicle(testContext.fleetId, zone.id, {
        odometer_mi: 5000,
        last_service_odometer_mi: 0,
        service_interval_mi: 5000
      }, testContext);

      const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);
      const holdPayload = factories.maintenanceHoldPayload(vehicle.id, {
        override_allowed: false,
        allowed_miles_remaining: 0
      });
      await apiClient.createMaintenanceHold(testContext.fleetId, holdPayload, mechanicHeaders);

      const highRepRenter = await fixtures.setupHighRepRenter(testContext);

      const renterHeaders = buildUserHeaders(highRepRenter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_HOLD_NOT_OVERRIDABLE });
    });
  });

  describe('BR-10: Override quote rejected if distance estimate exceeds allowed miles', () => {
    it('should reject override when distance exceeds allowed miles', async () => {
      const { vehicle, zone } = await fixtures.setupMaintenanceOverrideVehicle(testContext);
      const highRepRenter = await fixtures.setupHighRepRenter(testContext);

      const renterHeaders = buildUserHeaders(highRepRenter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id, {
        distance_estimate_mi: 60
      });
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INSUFFICIENT_MAINTENANCE_OVERRIDE_MILES });
    });
  });

  describe('BR-11: Vehicle-zone mismatch blocks quote', () => {
    it('should reject quote when vehicle not in specified start zone', async () => {
      const zoneA = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
      const zoneB = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
      const vehicle = await fixtures.createTestVehicle(testContext.fleetId, zoneA.id, undefined, testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, zoneB.id, zoneB.id);
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_ZONE_MISMATCH });
    });
  });

  describe('BR-12: Zone locked (SOFT/HARD) blocks new rental quote', () => {
    it('should reject quote for vehicle in HARD_LOCK zone', async () => {
      const lockedZone = await fixtures.setupHardLockedZone(testContext);
      const vehicle = await fixtures.createTestVehicle(testContext.fleetId, lockedZone.id, undefined, testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, lockedZone.id, lockedZone.id);
      const response = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ZONE_LOCKED_FOR_RENTAL });
    });
  });

  describe('BR-13: Missing quote_id on rental confirmation', () => {
    it('should reject rental confirmation without quote_id', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const response = await apiClient.confirmRental(testContext.fleetId, {} as any, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_MISSING_QUOTE_ID });
    });
  });

  describe('BR-14: Quote tampering detected on rental confirmation', () => {
    it('should reject rental confirmation with tampered price', async () => {
      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
      const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
      const quote = getSuccessData(quoteResponse);

      const tamperedConfirmPayload = {
        quote_id: quote.quote_id,
        quoted_price_cents: quote.price_cents + 1
      };
      const response = await apiClient.confirmRental(testContext.fleetId, tamperedConfirmPayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_QUOTE_TAMPER });
    });
  });

  describe('BR-15: Prevent cancellation of non-confirmed rental', () => {
    it('should reject cancellation of active rental', async () => {
      const activeRentalSetup = await fixtures.setupActiveRentalScenario(testContext);

      const renterHeaders = buildUserHeaders(activeRentalSetup.renter.id, UserRole.RENTER);
      const response = await apiClient.cancelRental(testContext.fleetId, activeRentalSetup.rental.id, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_RENTAL_NOT_CANCELLABLE });
    });
  });

  describe('BR-16: Prevent return on a rental that is not active', () => {
    it('should reject return on confirmed but not active rental', async () => {
      const { vehicle, zone } = await fixtures.setupBasicAvailableVehicle(testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
      const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
      const quote = getSuccessData(quoteResponse);

      const confirmPayload = {
        quote_id: quote.quote_id,
        quoted_price_cents: quote.price_cents
      };
      const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
      const rental = getSuccessData(rentalResponse);

      const returnPayload = factories.returnRentalPayload(zone.id, 1100);
      const response = await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_RENTAL_NOT_ACTIVE });
    });
  });

  describe('BR-17: Return fails if destination zone does not exist', () => {
    it('should reject return to non-existent zone', async () => {
      const activeRentalSetup = await fixtures.setupActiveRentalScenario(testContext);

      const renterHeaders = buildUserHeaders(activeRentalSetup.renter.id, UserRole.RENTER);
      const bogusZoneId = 'zone_BOGUS'.repeat(4);
      const returnPayload = factories.returnRentalPayload(bogusZoneId, 1100);
      const response = await apiClient.returnRental(testContext.fleetId, activeRentalSetup.rental.id, returnPayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ZONE_NOT_FOUND });
    });
  });

  describe('BR-18: Return fails if destination zone is HARD_LOCK', () => {
    it('should reject return to HARD_LOCK zone', async () => {
      const activeRentalSetup = await fixtures.setupActiveRentalScenario(testContext);
      const hardLockedZone = await fixtures.setupHardLockedZone(testContext);

      const renterHeaders = buildUserHeaders(activeRentalSetup.renter.id, UserRole.RENTER);
      const returnPayload = factories.returnRentalPayload(hardLockedZone.id, 1100);
      const response = await apiClient.returnRental(testContext.fleetId, activeRentalSetup.rental.id, returnPayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ZONE_LOCKED_FOR_RETURN });
    });
  });

  describe('BR-19: Reject return with odometer rollback', () => {
    it('should reject return with end odometer < start odometer', async () => {
      const activeRentalSetup = await fixtures.setupActiveRentalScenario(testContext);

      const renterHeaders = buildUserHeaders(activeRentalSetup.renter.id, UserRole.RENTER);
      const returnPayload = factories.returnRentalPayload(activeRentalSetup.zone.id, 500);
      const response = await apiClient.returnRental(testContext.fleetId, activeRentalSetup.rental.id, returnPayload, renterHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ODOMETER_ROLLBACK });
    });
  });

  describe('BR-20: Mechanic cannot charge a non-EV vehicle', () => {
    it('should reject charge attempt on non-EV vehicle', async () => {
      const zone = await fixtures.createTestZone(testContext.fleetId, undefined, testContext);
      const nonEvVehicle = await fixtures.createTestVehicle(testContext.fleetId, zone.id, {
        class: VehicleClass.STANDARD
      }, testContext);

      const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);
      const chargePayload = { battery_pct: 80 };
      const response = await apiClient.chargeVehicle(testContext.fleetId, nonEvVehicle.id, chargePayload, mechanicHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_EV });
    });
  });

  describe('BR-21: Mechanic cannot service a vehicle not in maintenance state', () => {
    it('should reject service on non-maintenance vehicle', async () => {
      const { vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);

      const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);
      const servicePayload = { odometer_mi: 1100, service_notes: 'Test service' };
      const response = await apiClient.serviceVehicle(testContext.fleetId, vehicle.id, servicePayload, mechanicHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_IN_MAINTENANCE });
    });
  });

  describe('BR-22: Operator cannot mark clean a vehicle that isn\'t dirty', () => {
    it('should reject clean complete on non-dirty vehicle', async () => {
      const { vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      const cleanPayload = { action: 'clean_complete' as const };
      const response = await apiClient.updateVehicleServiceStatus(testContext.fleetId, vehicle.id, cleanPayload, operatorHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_DIRTY });
    });
  });

  describe('BR-23: Cannot release a maintenance hold that\'s already inactive', () => {
    it('should reject release of already released hold', async () => {
      const { hold } = await fixtures.setupMaintenanceOverrideVehicle(testContext);

      const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);
      await apiClient.releaseMaintenanceHold(testContext.fleetId, hold.id, mechanicHeaders);

      const response = await apiClient.releaseMaintenanceHold(testContext.fleetId, hold.id, mechanicHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_HOLD_NOT_ACTIVE });
    });
  });

  describe('BR-24: Reject creating vehicle of class not allowed in fleet', () => {
    it('should reject vehicle creation with disallowed class', async () => {
      const operatorUserId = generateValidUserId();
      const operatorHeaders = buildUserHeaders(operatorUserId, UserRole.FLEET_OPERATOR);
      const restrictedFleetPayload = factories.cityFleetPayload({
        allowed_vehicle_classes: [VehicleClass.ECONOMY]
      });
      const fleetResponse = await apiClient.createCityFleet(restrictedFleetPayload, operatorHeaders);
      const restrictedFleet = getSuccessData(fleetResponse);

      const zone = await fixtures.createTestZone(restrictedFleet.id, undefined, undefined);

      const mechanicHeaders = buildUserHeaders(generateValidUserId(), UserRole.MECHANIC);
      const luxuryVehiclePayload = factories.vehiclePayload({
        class: VehicleClass.LUXURY,
        current_zone_id: zone.id
      });
      const response = await apiClient.createVehicle(restrictedFleet.id, luxuryVehiclePayload, mechanicHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_CLASS_NOT_ALLOWED_IN_FLEET });
    });
  });

  describe('BR-25: New vehicle must have an initial zone assignment', () => {
    it('should reject vehicle creation without zone', async () => {
      const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);
      const vehiclePayload = factories.vehiclePayload();
      delete (vehiclePayload as any).current_zone_id;
      const response = await apiClient.createVehicle(testContext.fleetId, vehiclePayload, mechanicHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_VEHICLE_MUST_HAVE_ZONE });
    });
  });

  describe('BR-26: Prevent cross-fleet resource access', () => {
    it('should reject access to vehicle from different fleet', async () => {
      const operatorAId = generateValidUserId();
      const operatorAHeaders = buildUserHeaders(operatorAId, UserRole.FLEET_OPERATOR);
      const fleetAPayload = factories.cityFleetPayload();
      const fleetAResponse = await apiClient.createCityFleet(fleetAPayload, operatorAHeaders);
      const fleetA = getSuccessData(fleetAResponse);

      const operatorBId = generateValidUserId();
      const operatorBHeaders = buildUserHeaders(operatorBId, UserRole.FLEET_OPERATOR);
      const fleetBPayload = factories.cityFleetPayload();
      const fleetBResponse = await apiClient.createCityFleet(fleetBPayload, operatorBHeaders);
      const fleetB = getSuccessData(fleetBResponse);

      const zoneA = await fixtures.createTestZone(fleetA.id, undefined, undefined);
      const vehicleA = await fixtures.createTestVehicle(fleetA.id, zoneA.id, undefined, undefined);

      const response = await apiClient.getVehicle(fleetB.id, vehicleA.id, operatorBHeaders);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_FORBIDDEN });
    });
  });

  describe('BR-27: Reject invalid user role header', () => {
    it('should reject request with invalid user role', async () => {
      const invalidHeaders = {
        'X-User-ID': generateValidUserId(),
        'X-User-Role': 'ADMIN'
      };
      const response = await apiClient.getCityFleet(testContext.fleetId, invalidHeaders as any);

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER });
    });
  });

  describe('BR-28: Only modify renter-specific fields on renters', () => {
    it('should reject suspension attempt on non-renter user', async () => {
      const mechanic = await fixtures.createTestUser(testContext.fleetId, UserRole.MECHANIC, undefined, testContext);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      const response = await apiClient.updateUser(
        testContext.fleetId,
        mechanic.id,
        { suspended: true },
        operatorHeaders
      );

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_CANNOT_MODIFY_NON_RENTER });
    });
  });

  describe('BR-29: Reject patch with invalid or immutable fields', () => {
    it('should reject attempt to change user role', async () => {
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      const response = await apiClient.updateUser(
        testContext.fleetId,
        renter.id,
        { role: UserRole.MECHANIC } as any,
        operatorHeaders
      );

      validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_UPDATE_FIELDS });
    });
  });

  describe('ADV-1: Two renters attempt to rent the same vehicle concurrently', () => {
    it('should allow both quotes but only first confirmation should succeed', async () => {
      const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
      const renterA = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const renterB = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

      const renterAHeaders = buildUserHeaders(renterA.id, UserRole.RENTER);
      const renterBHeaders = buildUserHeaders(renterB.id, UserRole.RENTER);

      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
      const quoteResponseA = await apiClient.createQuote(testContext.fleetId, quotePayload, renterAHeaders);
      const quoteResponseB = await apiClient.createQuote(testContext.fleetId, quotePayload, renterBHeaders);

      const quoteA = getSuccessData(quoteResponseA);
      const quoteB = getSuccessData(quoteResponseB);

      const confirmPayloadA = {
        quote_id: quoteA.quote_id,
        quoted_price_cents: quoteA.price_cents
      };
      const rentalResponseA = await apiClient.confirmRental(testContext.fleetId, confirmPayloadA, renterAHeaders);
      const rentalA = handleApiResponse(rentalResponseA, 201);
      expect(rentalA.state).toBe(RentalState.CONFIRMED);

      const vehicleAfterA = await apiClient.getVehicle(testContext.fleetId, vehicle.id, renterAHeaders);
      expect(getSuccessData(vehicleAfterA).state).toBe(VehicleState.RESERVED);

      const confirmPayloadB = {
        quote_id: quoteB.quote_id,
        quoted_price_cents: quoteB.price_cents
      };
      const rentalResponseB = await apiClient.confirmRental(testContext.fleetId, confirmPayloadB, renterBHeaders);
      validateErrorEnvelope(rentalResponseB, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_RENTABLE });
    });
  });

  describe('ADV-2: Hold placed on vehicle during rental flow triggers cancellation', () => {
    it('should reject confirmation when maintenance hold is placed after quote', async () => {
      const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
      const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
      const mechanic = await fixtures.createTestUser(testContext.fleetId, UserRole.MECHANIC, undefined, testContext);

      const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
      const mechanicHeaders = buildUserHeaders(mechanic.id, UserRole.MECHANIC);

      const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
      const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
      const quote = getSuccessData(quoteResponse);

      const holdPayload = factories.maintenanceHoldPayload(vehicle.id, {
        reason_code: MaintenanceReasonCode.OIL_CHANGE,
        override_allowed: false,
        allowed_miles_remaining: 0
      });
      const holdResponse = await apiClient.createMaintenanceHold(testContext.fleetId, holdPayload, mechanicHeaders);
      getSuccessData(holdResponse);

      const confirmPayload = {
        quote_id: quote.quote_id,
        quoted_price_cents: quote.price_cents
      };
      const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
      validateErrorEnvelope(rentalResponse, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_RENTABLE });

      const vehicleAfter = await apiClient.getVehicle(testContext.fleetId, vehicle.id, mechanicHeaders);
      const vehicleData = getSuccessData(vehicleAfter);
      expect(vehicleData.state).toBe(VehicleState.AVAILABLE);
      expect(vehicleData.flags).toContain('MAINT_HOLD');
    });
  });

  describe('ADV-3: Manual zone lock during rental prevents return to that zone', () => {
    it('should prevent return to zone that gets locked during rental', async () => {
      const { zone, rental } = await fixtures.setupActiveRentalScenario(testContext);
      const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);
      const renterHeaders = buildUserHeaders(testContext.captures.renterId!, UserRole.RENTER);

      const lockResponse = await apiClient.updateParkingZone(
        testContext.fleetId,
        zone.id,
        { lock_state: ZoneLockState.HARD_LOCK },
        operatorHeaders
      );
      getSuccessData(lockResponse);

      const returnPayload = factories.returnRentalPayload(zone.id, 1050);
      const returnResponse = await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
      validateErrorEnvelope(returnResponse, { expectedErrorId: ApiErrorId.ERR_ZONE_LOCKED_FOR_RETURN });
    });
  });

  describe('ADV-6: Mixed infractions (damage + late) in one return are handled correctly', () => {
    // SKIPPED: This test claims to test late return but never advances the fleet action counter to simulate lateness
    it.skip('should handle late return with damage without returning error', async () => {
      const { rental } = await fixtures.setupActiveRentalScenario(testContext);
      const renterHeaders = buildUserHeaders(testContext.captures.renterId!, UserRole.RENTER);

      const returnPayload = {
        end_zone_id: testContext.captures.zoneId!,
        end_odometer_mi: 1050,
        damage_grade: DamageLevel.MAJOR
      };

      const returnResponse = await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);

      const returnedRental = handleApiResponse(returnResponse, 200);

      expect(returnedRental.state).toBe(RentalState.INFRACTION_REVIEW);

      const vehicleAfter = await apiClient.getVehicle(testContext.fleetId, testContext.captures.vehicleId!, renterHeaders);
      expect(getSuccessData(vehicleAfter).state).toBe(VehicleState.NEEDS_MAINTENANCE);
    });
  });

  describe('Boundary Limits (LIMIT)', () => {
    describe('LIMIT-1: Mileage ledger near service threshold', () => {
      it('should reject rental when vehicle is at service threshold', async () => {
        const { zone } = await fixtures.setupBasicAvailableVehicle(testContext);
        const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);

        const vehiclePayload = factories.vehiclePayload({
          current_zone_id: zone.id,
          odometer_mi: 5000,
          last_service_odometer_mi: 0,
          service_interval_mi: 5000
        });

        const mechanicHeaders = buildUserHeaders(testContext.captures.mechanicId!, UserRole.MECHANIC);
        const vehicleResponse = await apiClient.createVehicle(testContext.fleetId, vehiclePayload, mechanicHeaders);
        const vehicle = getSuccessData(vehicleResponse);

        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

        validateErrorEnvelope(quoteResponse, { expectedErrorId: ApiErrorId.ERR_VEHICLE_NOT_RENTABLE });
      });
    });

    describe('LIMIT-4: Override distance exactly at limit', () => {
      it('should allow override rental when distance equals allowed miles', async () => {
        const { vehicle, zone } = await fixtures.setupMaintenanceOverrideVehicle(testContext);
        const renter = await fixtures.setupHighRepRenter(testContext);

        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id, {
          distance_estimate_mi: 50
        });
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);

        const quote = getSuccessData(quoteResponse);
        expect(quote.quote_id).toBeDefined();
      });
    });

    describe('LIMIT-5: Late return exactly on the boundary of quoted duration', () => {
      it('should not be considered late when returned exactly at duration limit', async () => {
        const { rental } = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(testContext.captures.renterId!, UserRole.RENTER);

        const returnPayload = factories.returnRentalPayload(testContext.captures.zoneId!, 1050);
        const returnResponse = await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);

        const returnedRental = getSuccessData(returnResponse);
        expect(returnedRental.state).toBe(RentalState.COMPLETED);
      });
    });
  });

  describe('Cross-Resource Effects (XRES)', () => {
    describe('XRES-1: Rental completion updates Zone Balance Ledger', () => {
      it('should credit destination zone surplus by +1 on return', async () => {
        const { rental } = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(testContext.captures.renterId!, UserRole.RENTER);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const initialLedger = await apiClient.getZoneBalanceLedger(testContext.fleetId, operatorHeaders);
        const initialBalance = getSuccessData(initialLedger);
        const zoneEntry = initialBalance.find(entry => entry.zone_id === testContext.captures.zoneId!);
        const initialSurplus = zoneEntry?.surplus || 0;

        const returnPayload = factories.returnRentalPayload(testContext.captures.zoneId!, 1050);
        const returnResponse = await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
        getSuccessData(returnResponse);

        const finalLedger = await apiClient.getZoneBalanceLedger(testContext.fleetId, operatorHeaders);
        const finalBalance = getSuccessData(finalLedger);
        const finalZoneEntry = finalBalance.find(entry => entry.zone_id === testContext.captures.zoneId!);
        const finalSurplus = finalZoneEntry?.surplus || 0;

        expect(finalSurplus).toBe(initialSurplus + 1);
      });
    });

    describe('XRES-2: Rental start updates Zone Balance Ledger', () => {
      it('should debit start zone surplus by -1 on rental activation', async () => {
        const { zone, vehicle } = await fixtures.setupBasicAvailableVehicle(testContext);
        const renter = await fixtures.createTestUser(testContext.fleetId, UserRole.RENTER, undefined, testContext);
        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const quotePayload = factories.quotePayload(vehicle.id, zone.id, zone.id);
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
        const quote = getSuccessData(quoteResponse);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
        const rental = getSuccessData(rentalResponse);

        const initialLedger = await apiClient.getZoneBalanceLedger(testContext.fleetId, operatorHeaders);
        const initialBalance = getSuccessData(initialLedger);
        const zoneEntry = initialBalance.find(entry => entry.zone_id === zone.id);
        const initialSurplus = zoneEntry?.surplus || 0;

        const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
        getSuccessData(unlockResponse);

        const finalLedger = await apiClient.getZoneBalanceLedger(testContext.fleetId, operatorHeaders);
        const finalBalance = getSuccessData(finalLedger);
        const finalZoneEntry = finalBalance.find(entry => entry.zone_id === zone.id);
        const finalSurplus = finalZoneEntry?.surplus || 0;

        expect(finalSurplus).toBe(initialSurplus - 1);
      });
    });

    describe('XRES-3: Maintenance override rental affects Vehicle Mileage Ledger', () => {
      it('should subtract override credit from recorded miles on return', async () => {
        const { vehicle } = await fixtures.setupMaintenanceOverrideVehicle(testContext);
        const renter = await fixtures.setupHighRepRenter(testContext);
        const renterHeaders = buildUserHeaders(renter.id, UserRole.RENTER);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const quotePayload = factories.quotePayload(vehicle.id, testContext.captures.zoneId!, testContext.captures.zoneId!, {
          distance_estimate_mi: 25
        });
        const quoteResponse = await apiClient.createQuote(testContext.fleetId, quotePayload, renterHeaders);
        const quote = getSuccessData(quoteResponse);

        const confirmPayload = {
          quote_id: quote.quote_id,
          quoted_price_cents: quote.price_cents
        };
        const rentalResponse = await apiClient.confirmRental(testContext.fleetId, confirmPayload, renterHeaders);
        const rental = getSuccessData(rentalResponse);

        const unlockResponse = await apiClient.unlockRental(testContext.fleetId, rental.id, renterHeaders);
        const activeRental = getSuccessData(unlockResponse);

        const initialLedgerResponse = await apiClient.getVehicleMileageLedger(testContext.fleetId, vehicle.id, operatorHeaders);
        const initialLedger = getSuccessData(initialLedgerResponse);

        const returnPayload = factories.returnRentalPayload(testContext.captures.zoneId!, vehicle.odometer_mi + 50);
        const returnResponse = await apiClient.returnRental(testContext.fleetId, activeRental.id, returnPayload, renterHeaders);
        getSuccessData(returnResponse);

        const finalLedgerResponse = await apiClient.getVehicleMileageLedger(testContext.fleetId, vehicle.id, operatorHeaders);
        const finalLedger = getSuccessData(finalLedgerResponse);

        expect(finalLedger.override_miles_credit).toBe(0);

        const expectedMiles = 50 - initialLedger.override_miles_credit;
        const actualMiles = (finalLedger.miles_since_service || 0) - (initialLedger.miles_since_service || 0);
        expect(actualMiles).toBe(expectedMiles);
      });
    });

    describe('XRES-4: Late return increments late_counter and reduces reputation', () => {
      it('should update renter reputation ledger on late return', async () => {
        const { rental } = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(testContext.captures.renterId!, UserRole.RENTER);
        const operatorHeaders = buildUserHeaders(testContext.captures.operatorId!, UserRole.FLEET_OPERATOR);

        const initialLedger = await apiClient.getRenterReputationLedger(testContext.fleetId, testContext.captures.renterId!, operatorHeaders);
        const initialRep = getSuccessData(initialLedger);

        const returnPayload = factories.returnRentalPayload(testContext.captures.zoneId!, 1050);
        const returnResponse = await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
        const returnedRental = getSuccessData(returnResponse);

        if (returnedRental.state === RentalState.INFRACTION_REVIEW) {
          const reviewPayload = { resolution: 'approve' as const };
          await apiClient.reviewRental(testContext.fleetId, rental.id, reviewPayload, operatorHeaders);
        }

        const finalLedger = await apiClient.getRenterReputationLedger(testContext.fleetId, testContext.captures.renterId!, operatorHeaders);
        const finalRep = getSuccessData(finalLedger);

        expect(finalRep.late_counter).toBe(initialRep.late_counter + 1);
        expect(finalRep.reputation_score).toBeLessThan(initialRep.reputation_score);
      });
    });

    describe('XRES-5: Major damage infraction does not auto-suspend', () => {
      it('should not auto-suspend for damage without late return trigger', async () => {
        const { rental } = await fixtures.setupActiveRentalScenario(testContext);
        const renterHeaders = buildUserHeaders(testContext.captures.renterId!, UserRole.RENTER);

        const returnPayload = {
          end_zone_id: testContext.captures.zoneId!,
          end_odometer_mi: 1050,
          damage_grade: DamageLevel.MAJOR
        };
        const returnResponse = await apiClient.returnRental(testContext.fleetId, rental.id, returnPayload, renterHeaders);
        const returnedRental = getSuccessData(returnResponse);

        expect(returnedRental.state).toBe(RentalState.INFRACTION_REVIEW);

        const vehicleAfter = await apiClient.getVehicle(testContext.fleetId, testContext.captures.vehicleId!, renterHeaders);
        expect(getSuccessData(vehicleAfter).state).toBe(VehicleState.NEEDS_MAINTENANCE);

        const renterAfter = await apiClient.getUser(testContext.fleetId, testContext.captures.renterId!, renterHeaders);
        const renterData = getSuccessData(renterAfter);
        expect(renterData.suspended).toBeFalsy();
      });
    });
  });
});