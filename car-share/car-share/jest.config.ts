import type { Config } from '@jest/types';

const config: Config.InitialOptions = {
  // Use ts-jest preset for seamless TypeScript support
  preset: 'ts-jest',

  // Run tests in a Node.js environment (suitable for most backend libs/services)
  testEnvironment: 'node',

  // Look for any *.test.ts files inside a test_suite folder
  testMatch: ['**/test_suite/*.test.ts'],

  // Point ts-jest at the project's root tsconfig for path mapping & compiler options
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json',
    },
  },

  // Ensure resources spun up in setup are cleaned after the suite finishes
  globalSetup: '<rootDir>/test_suite/globalSetup.ts',
  globalTeardown: '<rootDir>/test_suite/teardown.ts',
  setupFilesAfterEnv: ['<rootDir>/console-sanitizer.ts'],

  // Sanitise aggregated results to avoid circular references when --json flag is used
  testResultsProcessor: '<rootDir>/jest-results-processor.js',
};

export default config;