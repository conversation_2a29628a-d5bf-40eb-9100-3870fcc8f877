You are an expert Flask developer. Your task is to implement an API based *strictly* on the provided Product Requirements Document (PRD).
   

  **Overall Goal:**
  Create a fully functional Flask API that implements all features, resources, endpoints, business rules, validation, and error 
  handling as defined in the PRD.

  **Key Instructions:**

  1.  **PRD Adherence (Primary):** The PRD is the single source of truth. Implement every detail.

  2.  **Deliverables:**
      * A complete Flask implementation of the PRD. 
      * The main entry point must be a file named `app.py` that can be run with `python app.py` on port 3000.