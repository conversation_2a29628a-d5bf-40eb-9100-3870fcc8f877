name: Run Tests

on:
  workflow_dispatch:
    inputs:
      name:
        description: 'Name of the implementation group to test (folder name in implementations/)'
        required: true
        type: string

jobs:
  discover-implementations:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.generate-matrix.outputs.matrix }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
      
      - name: Discover implementations
        id: generate-matrix
        run: |
          set -euo pipefail
          
          IMPL_FOLDER="implementations/${{ inputs.name }}"
          
          # Check if the implementation folder exists
          if [[ ! -d "$IMPL_FOLDER" ]]; then
            echo "Error: Implementation folder '$IMPL_FOLDER' not found"
            exit 1
          fi
          
          # Build matrix from existing implementations
          matrix_json='{"include":['
          first=true
          
          for impl in "$IMPL_FOLDER"/*; do
            if [[ -d "$impl" ]]; then
              impl_name=$(basename "$impl")
              
              if [[ "$first" == "true" ]]; then
                first=false
              else
                matrix_json+=','
              fi
              
              matrix_json+='{"folder":"'$impl_name'"}'
            fi
          done
          
          matrix_json+=']}'
          
          echo "Found implementations: $matrix_json"
          echo "matrix=$matrix_json" >> $GITHUB_OUTPUT

  test-implementations:
    needs: discover-implementations
    runs-on: ubuntu-latest
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.discover-implementations.outputs.matrix) }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Run tests for ${{ matrix.folder }}
        run: |
          set -euo pipefail
          
          # Get branch name (which is the main folder name)
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          echo "Branch name: $BRANCH_NAME"
          
          # Find the main folder
          MAIN_FOLDER=""
          for dir in */; do
            dir_name=${dir%/}
            if [[ "$dir_name" != ".github" && ! "$dir_name" =~ ^implementation[0-9]*$ && "$dir_name" != "implementations" ]]; then
              if [[ "$dir_name" == "$BRANCH_NAME" ]]; then
                MAIN_FOLDER="$dir_name"
                break
              fi
            fi
          done
          
          if [[ -z "$MAIN_FOLDER" ]]; then
            echo "Error: Could not find main folder with name $BRANCH_NAME"
            exit 1
          fi
          
          echo "Main folder found: $MAIN_FOLDER"
          
          # Navigate to main folder
          cd "$MAIN_FOLDER"
          
          # Install dependencies
          echo "Installing dependencies..."
          npm install
          
          # Run tests with API_START_COMMAND pointing to the implementation
          IMPL_PATH="../implementations/${{ inputs.name }}/${{ matrix.folder }}"
          echo "Running tests for implementation at: $IMPL_PATH"
          
          # Determine if this is a Flask or Node implementation
          if [[ "${{ matrix.folder }}" == flask-* ]]; then
            echo "Detected Flask implementation"
            
            # Check if Flask implementation needs setup
            if [[ -f "$IMPL_PATH/requirements.txt" ]]; then
              echo "Setting up Flask implementation..."
              cd "$IMPL_PATH"
              
              # Create virtual environment if it doesn't exist
              if [[ ! -d "venv" ]]; then
                echo "Creating virtual environment..."
                python3 -m venv venv
              fi
              
              # Activate virtual environment and install dependencies
              echo "Installing Flask dependencies..."
              source venv/bin/activate
              pip install -r requirements.txt
              
              cd - > /dev/null
            fi
            
            # Use Flask start command
            API_START_COMMAND="cd $IMPL_PATH && python3 app.py"
            
            # Quick test to see if server can start
            echo "Testing if Flask server can start..."
            cd "$IMPL_PATH"
            source venv/bin/activate 2>/dev/null || true
            timeout 10s python3 app.py 2>&1 | head -20 || echo "Server startup test completed"
            cd - > /dev/null
            
          else
            echo "Detected Node implementation"
            
            # Check if implementation needs building
            if [[ -f "$IMPL_PATH/package.json" ]]; then
              echo "Checking if implementation needs building..."
              cd "$IMPL_PATH"
              
              # Install dependencies if needed
              if [[ ! -d "node_modules" ]]; then
                echo "Installing implementation dependencies..."
                npm install
              fi
              
              # Check if this project needs building
              # First, check if there's a build script
              if grep -q '"build"' package.json; then
                echo "Build script found in package.json"
                
                # Check the start script to determine the entry point
                START_SCRIPT=$(grep -o '"start":[[:space:]]*"[^"]*"' package.json | cut -d'"' -f4)
                echo "Start script: $START_SCRIPT"
                
                # Extract the file that the start script runs
                ENTRY_FILE=""
                if [[ "$START_SCRIPT" =~ node[[:space:]]+([^ ]+) ]]; then
                  ENTRY_FILE="${BASH_REMATCH[1]}"
                elif [[ "$START_SCRIPT" =~ ts-node[[:space:]]+([^ ]+) ]]; then
                  # If using ts-node, no build needed
                  ENTRY_FILE="${BASH_REMATCH[1]}"
                  echo "Project uses ts-node, skipping build"
                fi
                
                # Check if the entry file exists
                if [[ -n "$ENTRY_FILE" ]] && [[ ! -f "$ENTRY_FILE" ]]; then
                  echo "Entry file '$ENTRY_FILE' not found, running build..."
                  npm run build || echo "Build failed, continuing anyway..."
                else
                  echo "Entry file exists or using ts-node, skipping build"
                fi
              else
                echo "No build script found, assuming no build needed"
              fi
              
              # Verify the server can be started
              echo "Verifying server configuration..."
              if [[ -f "server.js" ]] || [[ -f "index.js" ]] || [[ -f "app.js" ]] || [[ -f "dist/server.js" ]] || [[ -f "src/server.ts" ]]; then
                echo "✅ Found server file"
              else
                echo "⚠️  No obvious server file found, will attempt to start anyway"
              fi
              
              cd - > /dev/null
            fi
            
            # Use Node start command
            API_START_COMMAND="cd $IMPL_PATH && npm start"
            
            # Quick test to see if server can start
            echo "Testing if server can start..."
            cd "$IMPL_PATH"
            timeout 10s npm start 2>&1 | head -20 || echo "Server startup test completed"
            cd - > /dev/null
          fi
          
          # Run the test command with proper start command
          echo "Using start command: $API_START_COMMAND"
          API_START_COMMAND="$API_START_COMMAND" API_URL="http://127.0.0.1:3000" npm run test || true
          
          # Check if results.json was created
          if [[ -f "results.json" ]]; then
            echo "Test results generated successfully"
            
            # Parse results to create summary
            if [[ -f "../parse-test-results.js" ]]; then
              echo "Parsing test results..."
              node ../parse-test-results.js results.json test-summary.json
              cp test-summary.json "../test-summary-${{ matrix.folder }}.json"
              cp test-summary.md "../test-summary-${{ matrix.folder }}.md"
            else
              echo "Warning: parse-test-results.js not found, copying raw results"
              cp results.json "../test-summary-${{ matrix.folder }}.json"
            fi
          else
            echo "Warning: results.json not found"
            # Create a failure summary
            echo '{"totalTests": 0, "passedTests": 0, "failedTests": 0, "passPercentage": 0, "success": false, "error": "No test results found"}' > "../test-summary-${{ matrix.folder }}.json"
          fi
      
      - name: Display test results
        if: always()
        run: |
          # Display test summary in GitHub Actions
          if [[ -f "test-summary-${{ matrix.folder }}.md" ]]; then
            echo "## 🧪 Test Results for ${{ matrix.folder }}" >> $GITHUB_STEP_SUMMARY
            cat "test-summary-${{ matrix.folder }}.md" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi
      
      - name: Upload test summary
        uses: actions/upload-artifact@v4
        with:
          name: test-summary-${{ matrix.folder }}
          path: test-summary-${{ matrix.folder }}.json
          if-no-files-found: warn

  aggregate-results:
    needs: test-implementations
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            aggregate-test-results.js
      
      - name: Download all test summaries
        uses: actions/download-artifact@v4
        with:
          pattern: test-summary-*
          path: test-summaries
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Create aggregate report
        run: |
          node aggregate-test-results.js test-summaries "${{ inputs.name }}"
          
          # Add to GitHub Actions summary
          if [[ -f aggregate-report.md ]]; then
            cat aggregate-report.md >> $GITHUB_STEP_SUMMARY
          fi
      
      - name: Upload aggregate report
        uses: actions/upload-artifact@v4
        with:
          name: aggregate-test-report
          path: |
            aggregate-report.md
            aggregate-report.json 
