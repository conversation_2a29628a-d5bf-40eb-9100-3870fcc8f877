name: Create Implementations

on:
  workflow_dispatch:
    inputs:
      implementations:
        description: 'Number of implementations to create per type'
        required: false
        default: '3'
        type: string
      name:
        description: 'Name for the implementation group'
        required: true
        default: 'Attempt1'
        type: string
      model:
        description: 'Claude model to use (e.g., claude-opus-4-0, claude-3-5-haiku-latest).'
        required: false
        default: 'claude-sonnet-4-20250514'
        type: string
      type:
        description: 'Implementation type to create: "node" or "flask" (leave empty for both)'
        required: false
        default: 'both'
        type: string

concurrency:
  group: '${{ github.workflow }}-${{ github.ref }}'
  cancel-in-progress: true

jobs:
  create-implementations:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0  # Full history needed for rebasing
      
      - name: Create implementation folders
        run: |
          set -euo pipefail
          # Get branch name
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          echo "Branch name: $BRANCH_NAME"
          
          # Find the main folder (same name as branch, ignore .github and implementation*)
          MAIN_FOLDER=""
          for dir in */; do
            dir_name=${dir%/}
            if [[ "$dir_name" != ".github" && ! "$dir_name" =~ ^implementation[0-9]*$ ]]; then
              if [[ "$dir_name" == "$BRANCH_NAME" ]]; then
                MAIN_FOLDER="$dir_name"
                break
              fi
            fi
          done
          
          if [[ -z "$MAIN_FOLDER" ]]; then
            echo "Error: Could not find main folder with name $BRANCH_NAME"
            exit 1
          fi
          
          echo "Main folder found: $MAIN_FOLDER"
          
          # Check if required files exist in the main folder
          if [[ ! -f "$MAIN_FOLDER/PRD.md" ]]; then
            echo "Error: PRD.md not found in $MAIN_FOLDER"
            exit 1
          fi
          
          if [[ ! -f "$MAIN_FOLDER/imp-prompt-flask.txt" ]]; then
            echo "Error: imp-prompt-flask.txt not found in $MAIN_FOLDER"
            exit 1
          fi
          
          if [[ ! -f "$MAIN_FOLDER/imp-prompt-node.txt" ]]; then
            echo "Error: imp-prompt-node.txt not found in $MAIN_FOLDER"
            exit 1
          fi
          
          echo "Found PRD.md, imp-prompt-flask.txt, and imp-prompt-node.txt in $MAIN_FOLDER"
          
          # Create implementations folder if it doesn't exist
          if [[ ! -d "implementations" ]]; then
            mkdir implementations
            echo "Created implementations folder"
          fi
          
          # Create named folder inside implementations
          NAMED_FOLDER="implementations/${{ inputs.name }}"
          mkdir -p "$NAMED_FOLDER"
          echo "Created $NAMED_FOLDER"
          
          # Create implementation folders
          NUM_IMPLEMENTATIONS=${{ inputs.implementations }}
          IMPL_TYPE="${{ inputs.type }}"
          
          # Validate implementation type
          if [[ "$IMPL_TYPE" != "node" ]] && [[ "$IMPL_TYPE" != "flask" ]] && [[ "$IMPL_TYPE" != "both" ]]; then
            echo "Error: Invalid implementation type '$IMPL_TYPE'. Must be 'node', 'flask', or 'both'"
            exit 1
          fi
          
          # Determine what to create
          CREATE_FLASK=true
          CREATE_NODE=true
          if [[ "$IMPL_TYPE" == "flask" ]]; then
            CREATE_NODE=false
            echo "Creating only Flask implementations"
          elif [[ "$IMPL_TYPE" == "node" ]]; then
            CREATE_FLASK=false
            echo "Creating only Node implementations"
          else
            echo "Creating both Flask and Node implementations"
          fi
          
          # Calculate total count
          TOTAL_COUNT=0
          if [[ "$CREATE_FLASK" == true ]]; then
            TOTAL_COUNT=$((TOTAL_COUNT + NUM_IMPLEMENTATIONS))
          fi
          if [[ "$CREATE_NODE" == true ]]; then
            TOTAL_COUNT=$((TOTAL_COUNT + NUM_IMPLEMENTATIONS))
          fi
          
          echo "Creating $TOTAL_COUNT implementation folders in total"
          
          # Create flask implementation folders
          if [[ "$CREATE_FLASK" == true ]]; then
            for ((i=1; i<=NUM_IMPLEMENTATIONS; i++)); do
              IMPL_FOLDER="$NAMED_FOLDER/flask-implementation$i"
              
              echo "Creating $IMPL_FOLDER"
              mkdir -p "$IMPL_FOLDER"
              cp "$MAIN_FOLDER/PRD.md" "$IMPL_FOLDER/"
              cp "$MAIN_FOLDER/imp-prompt-flask.txt" "$IMPL_FOLDER/"
              echo "Created $IMPL_FOLDER with PRD.md and imp-prompt-flask.txt"
            done
          fi
          
          # Create node implementation folders
          if [[ "$CREATE_NODE" == true ]]; then
            for ((i=1; i<=NUM_IMPLEMENTATIONS; i++)); do
              IMPL_FOLDER="$NAMED_FOLDER/node-implementation$i"
              
              echo "Creating $IMPL_FOLDER"
              mkdir -p "$IMPL_FOLDER"
              cp "$MAIN_FOLDER/PRD.md" "$IMPL_FOLDER/"
              cp "$MAIN_FOLDER/imp-prompt-node.txt" "$IMPL_FOLDER/"
              echo "Created $IMPL_FOLDER with PRD.md and imp-prompt-node.txt"
            done
          fi
          
          echo "Successfully created $TOTAL_COUNT implementation folders in $NAMED_FOLDER"
      
      - name: Save run metadata
        run: |
          set -euo pipefail
          NAMED_FOLDER="implementations/${{ inputs.name }}"
          
          # Determine the model name
          MODEL_NAME="${{ inputs.model }}"
          if [[ -z "$MODEL_NAME" ]]; then
            MODEL_NAME="default (Sonnet 4)"
          fi
          
          # Get implementation count
          IMPL_COUNT=${{ inputs.implementations }}
          IMPL_TYPE="${{ inputs.type }}"
          
          # Calculate counts based on type
          FLASK_COUNT=0
          NODE_COUNT=0
          if [[ "$IMPL_TYPE" == "both" ]] || [[ "$IMPL_TYPE" == "flask" ]]; then
            FLASK_COUNT=$IMPL_COUNT
          fi
          if [[ "$IMPL_TYPE" == "both" ]] || [[ "$IMPL_TYPE" == "node" ]]; then
            NODE_COUNT=$IMPL_COUNT
          fi
          TOTAL_COUNT=$((FLASK_COUNT + NODE_COUNT))
          
          # Create metadata file
          cat > "$NAMED_FOLDER/run-metadata.json" << EOF
          {
            "groupName": "${{ inputs.name }}",
            "model": "$MODEL_NAME",
            "createdAt": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "implementationType": "$IMPL_TYPE",
            "implementationCount": {
              "flask": $FLASK_COUNT,
              "node": $NODE_COUNT,
              "total": $TOTAL_COUNT
            }
          }
          EOF
          
          echo "Saved run metadata to $NAMED_FOLDER/run-metadata.json"
          cat "$NAMED_FOLDER/run-metadata.json"
      
      - name: Commit and push changes
        run: |
          set -euo pipefail
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .
          if ! git diff --staged --quiet; then
            # Build commit message based on what was created
            IMPL_TYPE="${{ inputs.type }}"
            if [[ "$IMPL_TYPE" == "both" ]]; then
              COMMIT_MSG="Create ${{ inputs.implementations }} flask and ${{ inputs.implementations }} node implementation folders for ${{ inputs.name }}"
            elif [[ "$IMPL_TYPE" == "flask" ]]; then
              COMMIT_MSG="Create ${{ inputs.implementations }} flask implementation folders for ${{ inputs.name }}"
            else
              COMMIT_MSG="Create ${{ inputs.implementations }} node implementation folders for ${{ inputs.name }}"
            fi
            
            git commit -m "$COMMIT_MSG"
            if [[ -n "${{ inputs.model }}" ]]; then
              git commit --amend -m "$COMMIT_MSG using model: ${{ inputs.model }}"
            fi
            git push
          else
            echo "No changes to commit"
          fi

  prepare-matrix:
    needs: create-implementations
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.generate-matrix.outputs.matrix }}
    steps:
      - name: Generate dynamic matrix
        id: generate-matrix
        run: |
          set -euo pipefail
          NUM_IMPLEMENTATIONS=${{ inputs.implementations }}
          NAMED_FOLDER="implementations/${{ inputs.name }}"
          IMPL_TYPE="${{ inputs.type }}"
          
          # Build the matrix JSON dynamically with include format
          matrix_json='{"include":['
          first=true
          
          # Add flask implementations if needed
          if [[ "$IMPL_TYPE" == "both" ]] || [[ "$IMPL_TYPE" == "flask" ]]; then
            for ((i=1; i<=NUM_IMPLEMENTATIONS; i++)); do
              if [ "$first" != true ]; then
                matrix_json+=','
              fi
              matrix_json+='{"folder":"flask-implementation'$i'","prompt":"imp-prompt-flask.txt"}'
              first=false
            done
          fi
          
          # Add node implementations if needed
          if [[ "$IMPL_TYPE" == "both" ]] || [[ "$IMPL_TYPE" == "node" ]]; then
            for ((i=1; i<=NUM_IMPLEMENTATIONS; i++)); do
              if [ "$first" != true ]; then
                matrix_json+=','
              fi
              matrix_json+='{"folder":"node-implementation'$i'","prompt":"imp-prompt-node.txt"}'
              first=false
            done
          fi
          
          matrix_json+=']}'
          
          echo "Generated matrix: $matrix_json"
          echo "matrix=$matrix_json" >> $GITHUB_OUTPUT

  generate-implementations:
    needs: prepare-matrix
    runs-on: ubuntu-latest
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.prepare-matrix.outputs.matrix) }}
      max-parallel: 10
    continue-on-error: true  # Allow job to continue even if individual implementations fail
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0  # Full history needed for rebasing
      
      - name: Pull latest changes
        run: |
          set -euo pipefail
          git pull origin ${{ github.ref_name }}
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Install Claude CLI
        run: |
          set -euo pipefail
          echo "Installing Claude Code CLI..."
          npm install -g @anthropic-ai/claude-code@latest
          echo "Claude Code CLI installed successfully"
      
      - name: Run Claude Code for ${{ matrix.folder }}
        working-directory: "implementations/${{ inputs.name }}/${{ matrix.folder }}"
        env:
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          BASH_DEFAULT_TIMEOUT_MS: "1800000"   # 30 minutes
          BASH_MAX_TIMEOUT_MS:     "1800000"
        run: |
          set -euo pipefail
          echo "Running Claude in sandboxed directory: $(pwd)"
          
          # Read the prompt file content
          PROMPT_CONTENT=$(cat "${{ matrix.prompt }}")
          
          # Log the prompt content
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "📝 PROMPT CONTENT FROM ${{ matrix.prompt }}:"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "$PROMPT_CONTENT"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo ""
          
          # Build Claude command with optional model parameter
          CLAUDE_CMD="claude -p \"$PROMPT_CONTENT\" --allowedTools \"Bash(git:*),View,GlobTool,GrepTool,BatchTool,Write,Update\" --max-turns 50"
          
          # Add model parameter if provided
          if [[ -n "${{ inputs.model }}" ]]; then
            CLAUDE_CMD="$CLAUDE_CMD --model ${{ inputs.model }}"
            echo "Using model: ${{ inputs.model }}"
          else
            echo "Using default model"
          fi
          
          # Log the full command
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "🚀 EXECUTING CLAUDE COMMAND:"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "$CLAUDE_CMD"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo ""
          echo "⏳ Starting Claude execution..."
          echo ""
          
          # Run Claude Code with the prompt
          eval "$CLAUDE_CMD" || {
            echo "⚠️ Claude failed to generate implementation for ${{ matrix.folder }}"
            echo "This is not critical - testing will skip this implementation"
            exit 0  # Exit successfully to allow workflow to continue
          }
      
      - name: Commit changes for ${{ matrix.folder }}
        run: |
          set -euo pipefail
          git config --local user.email "<EMAIL>"
          git config --local user.name "Claude Code Action"
          git add .
          
          if ! git diff --staged --quiet; then
            # Commit the changes
            git commit -m "🤖 Generate implementation for ${{ matrix.folder }}"
            
            # Simple retry loop for push
            for i in 1 2 3; do
              echo "Push attempt $i/3..."
              
              # Rebase on latest before pushing
              git pull --rebase --autostash origin "$GITHUB_REF_NAME"
              
              # Try to push
              if git push --follow-tags; then
                echo "✅ Successfully pushed changes for ${{ matrix.folder }}"
                break
              else
                echo "⚠️ Push failed, retrying in 3 seconds..."
                sleep 3
              fi
            done
          else
            echo "No changes to commit for ${{ matrix.folder }}"
          fi

  test-implementations:
    needs: [prepare-matrix, generate-implementations]
    runs-on: ubuntu-latest
    if: always() && needs.prepare-matrix.result == 'success'  # Run even if some implementations failed
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.prepare-matrix.outputs.matrix) }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
      
      - name: Pull latest changes
        run: |
          set -euo pipefail
          git pull origin ${{ github.ref_name }}
      
      - name: Check if implementation exists
        id: check-impl
        run: |
          IMPL_PATH="implementations/${{ inputs.name }}/${{ matrix.folder }}"
          
          # Check if the implementation directory exists and has actual code
          if [[ -d "$IMPL_PATH" ]]; then
            # Check if there are any source files (common patterns)
            if find "$IMPL_PATH" -name "*.js" -o -name "*.ts" -o -name "package.json" | grep -q .; then
              echo "✅ Implementation found at $IMPL_PATH"
              echo "exists=true" >> $GITHUB_OUTPUT
            else
              echo "⚠️ Implementation directory exists but appears empty: $IMPL_PATH"
              echo "exists=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "⚠️ Implementation not found at $IMPL_PATH - skipping tests"
            echo "exists=false" >> $GITHUB_OUTPUT
          fi
      
      - name: Setup Node.js
        if: steps.check-impl.outputs.exists == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Run tests for ${{ matrix.folder }}
        if: steps.check-impl.outputs.exists == 'true'
        run: |
          set -euo pipefail
          
          # Get branch name (which is the main folder name)
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          echo "Branch name: $BRANCH_NAME"
          
          # Find the main folder
          MAIN_FOLDER=""
          for dir in */; do
            dir_name=${dir%/}
            if [[ "$dir_name" != ".github" && ! "$dir_name" =~ ^implementation[0-9]*$ && "$dir_name" != "implementations" ]]; then
              if [[ "$dir_name" == "$BRANCH_NAME" ]]; then
                MAIN_FOLDER="$dir_name"
                break
              fi
            fi
          done
          
          if [[ -z "$MAIN_FOLDER" ]]; then
            echo "Error: Could not find main folder with name $BRANCH_NAME"
            exit 1
          fi
          
          echo "Main folder found: $MAIN_FOLDER"
          
          # Navigate to main folder
          cd "$MAIN_FOLDER"
          
          # Install dependencies
          echo "Installing dependencies..."
          npm install
          
          # Run tests with API_START_COMMAND pointing to the implementation
          IMPL_PATH="../implementations/${{ inputs.name }}/${{ matrix.folder }}"
          echo "Running tests for implementation at: $IMPL_PATH"
          
          # Determine if this is a Flask or Node implementation
          if [[ "${{ matrix.folder }}" == flask-* ]]; then
            echo "Detected Flask implementation"
            
            # Check if Flask implementation needs setup
            if [[ -f "$IMPL_PATH/requirements.txt" ]]; then
              echo "Setting up Flask implementation..."
              cd "$IMPL_PATH"
              
              # Create virtual environment if it doesn't exist
              if [[ ! -d "venv" ]]; then
                echo "Creating virtual environment..."
                python3 -m venv venv
              fi
              
              # Activate virtual environment and install dependencies
              echo "Installing Flask dependencies..."
              source venv/bin/activate
              pip install -r requirements.txt
              
              cd - > /dev/null
            fi
            
            # Use Flask start command
            API_START_COMMAND="cd $IMPL_PATH && python3 app.py"
            
          else
            echo "Detected Node implementation"
            
            # Check if implementation needs building
            if [[ -f "$IMPL_PATH/package.json" ]]; then
              echo "Checking if implementation needs building..."
              cd "$IMPL_PATH"
              
              # Install dependencies if needed
              if [[ ! -d "node_modules" ]]; then
                echo "Installing implementation dependencies..."
                npm install
              fi
              
              # Check if this project needs building
              # First, check if there's a build script
              if grep -q '"build"' package.json; then
                echo "Build script found in package.json"
                
                # Check the start script to determine the entry point
                START_SCRIPT=$(grep -o '"start":[[:space:]]*"[^"]*"' package.json | cut -d'"' -f4)
                echo "Start script: $START_SCRIPT"
                
                # Extract the file that the start script runs
                ENTRY_FILE=""
                if [[ "$START_SCRIPT" =~ node[[:space:]]+([^ ]+) ]]; then
                  ENTRY_FILE="${BASH_REMATCH[1]}"
                elif [[ "$START_SCRIPT" =~ ts-node[[:space:]]+([^ ]+) ]]; then
                  # If using ts-node, no build needed
                  ENTRY_FILE="${BASH_REMATCH[1]}"
                  echo "Project uses ts-node, skipping build"
                fi
                
                # Check if the entry file exists
                if [[ -n "$ENTRY_FILE" ]] && [[ ! -f "$ENTRY_FILE" ]]; then
                  echo "Entry file '$ENTRY_FILE' not found, running build..."
                  npm run build || echo "Build failed, continuing anyway..."
                else
                  echo "Entry file exists or using ts-node, skipping build"
                fi
              else
                echo "No build script found, assuming no build needed"
              fi
              
              # Verify the server can be started
              echo "Verifying server configuration..."
              if [[ -f "server.js" ]] || [[ -f "index.js" ]] || [[ -f "app.js" ]] || [[ -f "dist/server.js" ]] || [[ -f "src/server.ts" ]]; then
                echo "✅ Found server file"
              else
                echo "⚠️  No obvious server file found, will attempt to start anyway"
              fi
              
              cd - > /dev/null
            fi
            
            # Use Node start command
            API_START_COMMAND="cd $IMPL_PATH && npm start"
          fi
          
          # Run the test command with proper start command
          echo "Using start command: $API_START_COMMAND"
          API_START_COMMAND="$API_START_COMMAND" API_URL="http://127.0.0.1:3000" npm run test || true
          
          # Check if results.json was created
          if [[ -f "results.json" ]]; then
            echo "Test results generated successfully"
            
            # Parse results to create summary
            if [[ -f "../parse-test-results.js" ]]; then
              echo "Parsing test results..."
              node ../parse-test-results.js results.json test-summary.json
              cp test-summary.json "../test-summary-${{ matrix.folder }}.json"
            else
              echo "Warning: parse-test-results.js not found, copying raw results"
              cp results.json "../test-summary-${{ matrix.folder }}.json"
            fi
          else
            echo "Warning: results.json not found"
            # Create a failure summary
            echo '{"totalTests": 0, "passedTests": 0, "failedTests": 0, "passPercentage": 0, "success": false, "error": "No test results found"}' > "../test-summary-${{ matrix.folder }}.json"
          fi
      
      - name: Create skip summary for missing implementation
        if: steps.check-impl.outputs.exists == 'false'
        run: |
          # Create a skip summary for implementations that weren't generated
          echo '{"totalTests": 0, "passedTests": 0, "failedTests": 0, "passPercentage": 0, "success": false, "skipped": true, "reason": "Implementation not generated"}' > "test-summary-${{ matrix.folder }}.json"
      
      - name: Display test results
        if: always()
        run: |
          # Display test summary in GitHub Actions
          if [[ -f "test-summary-${{ matrix.folder }}.md" ]]; then
            echo "## 🧪 Test Results for ${{ matrix.folder }}" >> $GITHUB_STEP_SUMMARY
            cat "test-summary-${{ matrix.folder }}.md" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          elif [[ -f "test-summary-${{ matrix.folder }}.json" ]]; then
            # Check if it was skipped
            if grep -q '"skipped": true' "test-summary-${{ matrix.folder }}.json"; then
              echo "## ⏭️ Skipped: ${{ matrix.folder }}" >> $GITHUB_STEP_SUMMARY
              echo "Implementation was not generated" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
            fi
          fi
      
      - name: Upload test summary
        uses: actions/upload-artifact@v4
        with:
          name: test-summary-${{ matrix.folder }}
          path: test-summary-${{ matrix.folder }}.json
          if-no-files-found: warn
      
      - name: Commit test summary
        if: steps.check-impl.outputs.exists == 'true'
        run: |
          set -euo pipefail
          
          # Move summary to implementation folder
          if [[ -f "test-summary-${{ matrix.folder }}.json" ]]; then
            mv "test-summary-${{ matrix.folder }}.json" "implementations/${{ inputs.name }}/${{ matrix.folder }}/test-summary.json"
            
            git config --local user.email "<EMAIL>"
            git config --local user.name "GitHub Action"
            git add "implementations/${{ inputs.name }}/${{ matrix.folder }}/test-summary.json"
            
            if ! git diff --staged --quiet; then
              git commit -m "📊 Add test summary for ${{ matrix.folder }}"
              
              # Push with retry
              for i in 1 2 3; do
                echo "Push attempt $i/3..."
                git pull --rebase --autostash origin "$GITHUB_REF_NAME"
                if git push; then
                  echo "✅ Successfully pushed test summary for ${{ matrix.folder }}"
                  break
                else
                  echo "⚠️ Push failed, retrying in 3 seconds..."
                  sleep 3
                fi
              done
            fi
          fi 

  aggregate-results:
    needs: test-implementations
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            aggregate-test-results.js
      
      - name: Download all test summaries
        uses: actions/download-artifact@v4
        with:
          pattern: test-summary-*
          path: test-summaries
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Create aggregate report
        run: |
          node aggregate-test-results.js test-summaries "${{ inputs.name }}"
          
          # Add to GitHub Actions summary
          if [[ -f aggregate-report.md ]]; then
            cat aggregate-report.md >> $GITHUB_STEP_SUMMARY
          fi
      
      - name: Upload aggregate report
        uses: actions/upload-artifact@v4
        with:
          name: aggregate-test-report
          path: |
            aggregate-report.md
            aggregate-report.json 
