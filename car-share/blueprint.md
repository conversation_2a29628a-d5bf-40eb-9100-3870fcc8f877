This document provides the definitive, structured specification for the Free-Floating Car-Share API. It serves as the authoritative source of truth for all system components.

#### **User Roles**

| Role             | Core Responsibilities                                                                                             |
| :--------------- | :---------------------------------------------------------------------------------------------------------------- |
| `RENTER`         | Finds, quotes, rents, and returns vehicles. Manages their own reputation and rental history.                      |
| `FLEET_OPERATOR` | Manages the overall fleet, including users, parking zones, and vehicle lifecycle states. Resolves rental issues.    |
| `MECHANIC`       | Manages the physical state of vehicles, including registration, servicing, charging, and placing maintenance holds. |

---

#### **Resources & Fields**

**Resource: `CityFleet`**
The top-level container for all other resources.

| Field                     | Type           | Constraints                                                        | Description                                                               |
| :------------------------ | :------------- | :----------------------------------------------------------------- | :------------------------------------------------------------------------ |
| `id`                      | string         | readonly, pattern: `fleet_[0-9A-HJKMNP-TV-Z]{26}`                   | Unique identifier for the fleet.                                          |
| `city_code`               | string         | required, pattern: `^[A-Z]{2,3}$`                                   | The city code where the fleet operates.                                   |
| `name`                    | string         | required, minLength: 3, maxLength: 50                              | Human-readable name for the fleet.                                        |
| `phase_state`             | string (enum)  | readonly, `['NORMAL', 'HIGH_ALERT']`, default: `NORMAL`            | A system-wide state reflecting overall vehicle availability.              |
| `allowed_vehicle_classes` | array (string) | required, minItems: 1, items: `['ECONOMY', 'STANDARD', 'LUXURY', 'EV']` | List of vehicle classes permitted to operate in this fleet.             |
| `settings`                | object         | nullable                                                           | A placeholder for future fleet-specific policy settings.                  |

**Resource: `ParkingZone`**
A geofenced area where vehicles can be picked up and returned.

| Field             | Type          | Constraints                                                    | Description                                                                    |
| :---------------- | :------------ | :------------------------------------------------------------- | :----------------------------------------------------------------------------- |
| `id`              | string        | readonly, pattern: `zone_[0-9A-HJKMNP-TV-Z]{26}`                 | Unique identifier for the parking zone.                                        |
| `fleet_id`        | string        | required, pattern: `fleet_[0-9A-HJKMNP-TV-Z]{26}`                | Foreign key to the `CityFleet`.                                                |
| `code`            | string        | required, pattern: `^[A-Z]{3}-\d{2}$`, cannot start with `EA-` | A unique, human-readable code for the zone.                                    |
| `display_name`    | string        | required, minLength: 3, maxLength: 32                          | The name of the zone shown in user interfaces.                                 |
| `geojson`         | string        | required, GeoJSON Polygon format, max 50 vertices              | The geographical boundary of the zone.                                         |
| `target_vehicles` | integer       | required, min: 0, max: 500                                     | The desired number of vehicles to be available in this zone.                   |
| `ledger_surplus`  | integer       | readonly                                                       | A real-time reflection of the vehicle surplus from the `ZoneBalanceLedger`.    |
| `lock_state`      | string (enum) | `['OPEN', 'SOFT_LOCK', 'HARD_LOCK']`, default: `OPEN`          | The operational status of the zone, determined by vehicle surplus. A `HARD_LOCK` state can only be exited by a manual operator action via `PATCH /parking-zones/{zone_id}`. |
| `manual_lock`     | boolean       | default: `false`                                               | If `true`, the `lock_state` was set by an operator and automatic changes are paused. |
| `incentivized_classes` | array (string) | optional, items: `['ECONOMY', 'STANDARD', 'LUXURY', 'EV']` | Vehicle classes that receive reputation bonus when returned to this zone. |
| `discouraged_classes` | array (string) | optional, items: `['ECONOMY', 'STANDARD', 'LUXURY', 'EV']` | Vehicle classes that incur reputation penalty when returned to this zone. |

**Resource: `Vehicle`**
A car available for rent within the fleet.

| Field                      | Type           | Constraints                                                                                    | Description                                                                              |
| :------------------------- | :------------- | :--------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------- |
| `id`                       | string         | readonly, pattern: `veh_[0-9A-HJKMNP-TV-Z]{26}`                                                 | Unique identifier for the vehicle.                                                       |
| `fleet_id`                 | string         | required                                                                                       | Foreign key to the `CityFleet`.                                                          |
| `vin`                      | string         | required, length: 17, pattern: `^[A-HJ-NPR-Z0-9]{17}$`                                          | The Vehicle Identification Number.                                                       |
| `license_plate`            | string         | required, pattern: `^[A-Z0-9]{2,8}$`                                                            | The vehicle's license plate number.                                                      |
| `class`                    | string (enum)  | required, `['ECONOMY', 'STANDARD', 'LUXURY', 'EV']`                                            | The vehicle's classification, affecting rental price.                                    |
| `state`                    | string (enum)  | readonly, see State Machine, default: `AVAILABLE`                                              | The current operational state of the vehicle.                                            |
| `odometer_mi`              | integer        | required, min: 0                                                                               | The total mileage of the vehicle. Must be monotonically increasing.                      |
| `last_service_odometer_mi` | integer        | required, min: 0                                                                               | The odometer reading at the time of the last completed service. On vehicle creation, this is set to the initial odometer reading. |
| `service_interval_mi`      | integer        | required, min: 1000, max: 20000                                                                | The number of miles between required services.                                           |
| `current_zone_id`          | string         | nullable, pattern: `zone_[0-9A-HJKMNP-TV-Z]{26}`                                                | The zone where the vehicle is currently parked. `null` if in transit.                    |
| `battery_pct`              | integer        | nullable, min: 0, max: 100, required if `class` is `EV`                                        | The battery charge percentage, only for Electric Vehicles.                               |
| `flags`                    | array (string) | maxItems: 10, items: `['MAINT_HOLD', 'CLEAN_REQ', 'MAINT_OVERRIDE_GRANTED']`      | A list of operational flags indicating special conditions.                               |
| `miles_until_service`      | integer        | readonly                                                                                       | Derived field. Calculated as `service_interval_mi` - (`odometer_mi` - `last_service_odometer_mi`). |

**Resource: `User`**
A person interacting with the API.

| Field                 | Type           | Constraints                                                      | Description                                                                   |
| :-------------------- | :------------- | :--------------------------------------------------------------- | :---------------------------------------------------------------------------- |
| `id`                  | string         | readonly, pattern: `user_[0-9A-HJKMNP-TV-Z]{26}`                  | Unique identifier for the user.                                               |
| `fleet_id`            | string         | required                                                         | Foreign key to the `CityFleet`.                                               |
| `role`                | string (enum)  | required, `['RENTER', 'FLEET_OPERATOR', 'MECHANIC']`             | The user's role, which governs permissions.                                   |
| `name`                | string         | required, minLength: 2, maxLength: 40                            | The user's full name.                                                         |
| `email`               | string         | required, format: email                                          | The user's email address.                                                     |
| `phone`               | string         | nullable, pattern: `^\+[1-9]\d{1,14}$`                            | The user's E.164 formatted phone number.                                      |
| `reputation_score`    | integer        | required if role=`RENTER`, min: 0, max: 300, default: 100        | (Renter only) The renter's score, affecting privileges.                       |
| `luxury_unlock`       | boolean        | readonly                                                         | (Renter only) Derived field. True if `reputation_score` is high enough.         |
| `active_rental_id`    | string         | nullable, pattern: `rent_[0-9A-HJKMNP-TV-Z]{26}`                  | (Renter only) The ID of the user's current, active rental.                    |
| `late_return_history` | array (string) | readonly, maxItems: 5                                            | (Renter only) A list of the last 5 rental IDs that were returned late.        |
| `suspended`           | boolean        | default: `false`                                                 | (Renter only) If `true`, the renter cannot initiate new rental-related actions.    |

**Resource: `Quote`**
A temporary, price-locked offer for a specific rental.

| Field                     | Type    | Constraints                                        | Description                                                          |
| :------------------------ | :------ | :------------------------------------------------- | :------------------------------------------------------------------- |
| `id`                      | string  | readonly, pattern: `quot_[0-9A-HJKMNP-TV-Z]{26}`    | Unique identifier for the quote.                                     |
| `renter_id`               | string  | required                                           | The ID of the renter who requested the quote.                        |
| `vehicle_id`              | string  | required                                           | The ID of the vehicle being quoted.                                  |
| `start_zone_id`           | string  | required                                           | The pickup zone for the rental.                                      |
| `end_zone_id`             | string  | required                                           | The intended dropoff zone for the rental.                            |
| `distance_estimate_mi`    | integer | required, min: 0, max: 1000                        | The renter's estimated travel distance in miles.                     |
| `price_cents`             | integer | required                                           | The calculated price for the rental in cents.                        |
| `quoted_action_duration`  | integer | required                                           | The number of Fleet Action Counter increments allotted for the rental. |
| `price_hash`              | string  | required, pattern: `^[a-f0-9]{64}$`                | A SHA-256 hash of the price, to prevent tampering.                   |

**Resource: `Rental`**
The record of a vehicle rental by a user.

| Field                   | Type           | Constraints                                                                 | Description                                                                     |
| :---------------------- | :------------- | :-------------------------------------------------------------------------- | :------------------------------------------------------------------------------ |
| `id`                    | string         | readonly, pattern: `rent_[0-9A-HJKMNP-TV-Z]{26}`                             | Unique identifier for the rental.                                               |
| `renter_id`             | string         | required                                                                    | The ID of the renting user.                                                     |
| `vehicle_id`            | string         | required                                                                    | The ID of the rented vehicle.                                                   |
| `start_zone_id`         | string         | required                                                                    | The zone from which the rental started.                                         |
| `end_zone_id`           | string         | nullable                                                                    | The zone where the vehicle was returned.                                        |
| `quoted_price_cents`    | integer        | required                                                                    | The price in cents that was agreed upon at the time of confirmation.            |
| `distance_estimate_mi`  | integer        | required                                                                    | The renter's estimated mileage from the quote.                                  |
| `state`                 | string (enum)  | readonly, see State Machine                                                 | The current state of the rental process.                                        |
| `start_odometer_mi`     | integer        | nullable                                                                    | The vehicle's odometer reading when the rental became active.                   |
| `end_odometer_mi`       | integer        | nullable                                                                    | The vehicle's odometer reading upon return.                                     |
| `start_action_count`    | integer        | nullable                                                                    | The Fleet Action Counter value when the rental became active.                   |
| `quoted_action_duration` | integer        | required                                                                    | The number of Fleet Action Counter increments allotted for the rental.          |
| `surcharges_applied`    | array (string) | readonly                                                                    | A list of any surcharges added during the rental, e.g., for late returns.       |
| `discounts_applied`     | array (string) | readonly                                                                    | A list of any discounts applied to the rental.                                  |
| `reputation_delta`      | integer        | readonly                                                                    | The net change in the renter's reputation score resulting from this rental.     |
| `return_condition_report_id` | string    | nullable, pattern: `rept_[0-9A-HJKMNP-TV-Z]{26}`                             | The ID of the condition report filed upon the rental's completion.              |

**Resource: `ConditionReport`**
A report on a vehicle's condition, filed by a user.

| Field              | Type          | Constraints                                                    | Description                                                          |
| :----------------- | :------------ | :------------------------------------------------------------- | :------------------------------------------------------------------- |
| `id`               | string        | readonly, pattern: `rept_[0-9A-HJKMNP-TV-Z]{26}`                | Unique identifier for the report.                                    |
| `vehicle_id`       | string        | required                                                       | The ID of the vehicle being reported on.                             |
| `inspector_id`     | string        | required                                                       | The ID of the user filing the report.                                |
| `report_type`      | string (enum) | required, `['RETURN', 'SPOT', 'MAINT_CHECK']`                  | The context in which the report was filed.                           |
| `cleanliness_grade` | string (enum) | required, `['EXCELLENT', 'GOOD', 'FAIR', 'POOR']`                | The assessed cleanliness of the vehicle. See §3.5.                   |
| `damage_grade`     | string (enum) | required, `['NONE', 'MINOR', 'MAJOR']`                         | The assessed damage level of the vehicle. See §3.5.                  |
| `mileage_recorded` | integer       | required                                                       | The vehicle's odometer reading at the time of inspection.            |
| `notes`            | string        | nullable, minLength: 0, maxLength: 500                         | Free-text notes from the inspector.                                  |

**Resource: `MaintenanceHold`**
An operational hold placed on a vehicle by a Mechanic.

| Field                   | Type    | Constraints                                                  | Description                                                              |
| :---------------------- | :------ | :----------------------------------------------------------- | :----------------------------------------------------------------------- |
| `id`                    | string  | readonly, pattern: `hold_[0-9A-HJKMNP-TV-Z]{26}`              | Unique identifier for the hold.                                          |
| `fleet_id`              | string  | required                                                     | Foreign key to the `CityFleet`.                                          |
| `vehicle_id`            | string  | required                                                     | The ID of the vehicle under the hold.                                    |
| `placed_by`             | string  | required                                                     | The ID of the Mechanic who placed the hold.                              |
| `reason_code`           | string  | required, `['OIL_CHANGE', 'BRAKE_INSPECT', 'TIRE_ROTATION', 'OTHER']` | The reason for the maintenance hold.                                     |
| `override_allowed`      | boolean | required                                                     | Whether a high-reputation renter can override this hold for a short trip. |
| `allowed_miles_remaining` | integer | required, min: 0, max: 200                                   | If `override_allowed` is true, the number of miles the override is good for. |
| `active`                | boolean | required, default: `true`                                    | Whether the hold is currently active.                                    |

---

#### **State Machines**

**`Vehicle.state`**
`[AVAILABLE, RESERVED, IN_USE, NEEDS_CLEANING, NEEDS_MAINTENANCE, RETIRED]`
* `AVAILABLE` -> `RESERVED` (on rental confirmation)
* `RESERVED` -> `IN_USE` (on renter unlock, `current_zone_id` is set to `null`)
* `RESERVED` -> `AVAILABLE` (on rental cancellation, unless it was a maintenance override rental)
* `RESERVED` -> `NEEDS_MAINTENANCE` (on `MAJOR` damage report or on cancellation of a maintenance override rental, which also removes the `MAINT_OVERRIDE_GRANTED` flag)
* `IN_USE` -> `NEEDS_CLEANING` (on return with `POOR` cleanliness, `CLEAN_REQ` flag is added)
* `IN_USE` -> `NEEDS_MAINTENANCE` (on return when service is due, with `MAJOR` damage, or from a maintenance override rental)
* `IN_USE` -> `AVAILABLE` (on clean, on-time, no-damage return)
* `NEEDS_CLEANING` -> `AVAILABLE` (on cleaning complete)
* `NEEDS_CLEANING` -> `NEEDS_MAINTENANCE` (on cleaning complete, if `miles_until_service` <= 0 or a `MAINT_HOLD` flag is present)
* `NEEDS_MAINTENANCE` -> `RESERVED` (on maintenance override rental confirmation)
* `NEEDS_MAINTENANCE` -> `AVAILABLE` (on maintenance complete)
* `NEEDS_MAINTENANCE` -> `NEEDS_CLEANING` (on maintenance complete, but vehicle is also dirty)
* `ANY` -> `RETIRED` (on decommission)

**`Rental.state`**
`[CONFIRMED, ACTIVE, COMPLETED, CANCELLED, INFRACTION_REVIEW]`
* `CONFIRMED` -> `ACTIVE` (on renter unlock)
* `CONFIRMED` -> `CANCELLED` (on renter cancellation before unlock; renter's `active_rental_id` is cleared)
* `CONFIRMED` -> `CANCELLED` (on `MAJOR` damage report for the reserved vehicle)
* `ACTIVE` -> `COMPLETED` (on successful return)
* `ACTIVE` -> `INFRACTION_REVIEW` (on return with lateness or damage)
* `INFRACTION_REVIEW` -> `COMPLETED` (on operator review and resolution)

**`ParkingZone.lock_state`**
`[OPEN, SOFT_LOCK, HARD_LOCK]`
* `OPEN` -> `SOFT_LOCK` (triggered when vehicle surplus drops to a low threshold)
* `SOFT_LOCK` -> `HARD_LOCK` (triggered when vehicle surplus drops to a critical threshold)
* `SOFT_LOCK` -> `OPEN` (triggered when surplus recovers to normal)
* `HARD_LOCK` -> `OPEN` (via manual operator action)

---

#### **Ledgers**

| Ledger Name               | Purpose                                                                                                                                     | Schema                                                                        |
| :------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------ | :---------------------------------------------------------------------------- |
| **`FleetActionCounter`**  | Tracks the monotonically increasing action count for each fleet, serving as a logical clock for rental duration calculations.                | `fleet_id` (key), `action_count` (integer)                                    |
| **`ZoneBalanceLedger`** | Tracks the real-time surplus or deficit of vehicles in each parking zone against its target. Drives zone lock state changes.                  | `zone_id` (key), `surplus` (integer)                                          |
| **`VehicleMileageLedger`** | Tracks vehicle mileage for maintenance purposes, including credits from special overrides.                                                    | `vehicle_id` (key), `miles_since_service` (integer), `override_miles_credit` (integer) |
| **`RenterReputationLedger`**| Tracks a renter's score and history of late returns, which gate privileges and trigger penalties.                                           | `renter_id` (key), `reputation_score` (integer), `late_counter` (integer)     |

---

#### **Hierarchy of Truth**

In cases of conflicting rules or when multiple validations fail simultaneously, the following order of precedence dictates which error is returned:
 
1.  **User Suspension:** A `suspended` status on a `User` resource overrides all other permissions. A suspended user cannot initiate any rental activity.
2.  **Probationary Fleet Alert Restriction:** An attempt by a `PROBATIONARY` renter to quote a rental during a `HIGH_ALERT` fleet `phase_state` is rejected before any other rental-specific checks are made.
3.  **Vehicle State:** The `Vehicle.state` is absolute. A vehicle that is not `AVAILABLE` (or `NEEDS_MAINTENANCE` with a valid override) cannot be rented, regardless of any other condition.
4.  **Manual Zone Lock:** A `manual_lock: true` flag on a `ParkingZone` resource takes precedence over any automatic lock state adjustments triggered by the `ZoneBalanceLedger`.
5.  **Return Validation Precedence:** During vehicle return, validation checks are performed in a specific order. The system first checks for a `HARD_LOCK` on the destination zone. If that check passes, it then validates the odometer reading. An error is returned for the first validation that fails.
6.  **Hard Zone Lock:** A `lock_state` of `HARD_LOCK` on a `ParkingZone` unconditionally prevents vehicle returns to that zone.
7.  **Ledger-Based Restrictions:** Rules based on ledger values (e.g., `RenterReputationLedger` score, `ZoneBalanceLedger` surplus) are evaluated after the higher-precedence states are confirmed to be permissive.

---

#### **Endpoint Definitions**

| Method | Path                                                                | Summary                                                                  |
| :----- | :------------------------------------------------------------------ | :----------------------------------------------------------------------- |
| POST   | `/city-fleets`                                                      | Creates a new CityFleet container.                                       |
| GET    | `/city-fleets/{fleet_id}`                                           | Retrieves the details of a specific CityFleet.                           |
| GET    | `/city-fleets/{fleet_id}/summary`                                   | Retrieves a role-based summary for the fleet or renter.                  |
| POST   | `/city-fleets/{fleet_id}/users`                                     | Creates a new user within the fleet.                                     |
| GET    | `/city-fleets/{fleet_id}/users/{user_id}`                           | Retrieves the details of a specific user.                                |
| PATCH  | `/city-fleets/{fleet_id}/users/{user_id}`                           | Updates properties of a Renter, such as lifting a suspension.            |
| POST   | `/city-fleets/{fleet_id}/parking-zones`                             | Creates a new parking zone.                                              |
| PATCH  | `/city-fleets/{fleet_id}/parking-zones/{zone_id}`                   | Manually updates a zone's lock state or releases a manual lock.          |
| GET    | `/city-fleets/{fleet_id}/parking-zones/{zone_id}`                   | Retrieves the details of a parking zone.                                 |
| POST   | `/city-fleets/{fleet_id}/vehicles`                                  | Registers a new vehicle in the fleet. (Mechanic role required)           |
| POST   | `/city-fleets/{fleet_id}/vehicles/{vehicle_id}/retire`              | Permanently retires a vehicle from the fleet.                            |
| GET    | `/city-fleets/{fleet_id}/vehicles/{vehicle_id}`                     | Retrieves the details of a vehicle.                                      |
| PATCH  | `/city-fleets/{fleet_id}/vehicles/{vehicle_id}/service-status`      | Updates a vehicle's status after an action like cleaning.                |
| POST   | `/city-fleets/{fleet_id}/vehicles/{vehicle_id}/service`             | Marks a vehicle as serviced, resetting its maintenance clock.            |
| POST   | `/city-fleets/{fleet_id}/vehicles/{vehicle_id}/charge`              | Reports that an EV has been charged.                                     |
| POST   | `/city-fleets/{fleet_id}/rentals/quote`                             | Generates a price quote for a potential rental.                          |
| DELETE | `/city-fleets/{fleet_id}/rentals/quote/{quote_id}`                  | Deletes an unconfirmed quote.                                            |
| POST   | `/city-fleets/{fleet_id}/rentals`                                   | Confirms a quote to create a new rental.                                 |
| PATCH  | `/city-fleets/{fleet_id}/rentals/{rental_id}`                       | Performs an action on an active rental, such as unlocking the car.       |
| DELETE | `/city-fleets/{fleet_id}/rentals/{rental_id}`                       | Cancels a confirmed rental before it becomes active.                     |
| PATCH  | `/city-fleets/{fleet_id}/rentals/{rental_id}/return`                | Returns a vehicle to a zone, completing the rental.                      |
| PATCH  | `/city-fleets/{fleet_id}/rentals/{rental_id}/review`                | Resolves a rental flagged for an infraction, transitioning it to `COMPLETED`. |
| GET    | `/city-fleets/{fleet_id}/rentals/{rental_id}`                       | Retrieves the details of a specific rental.                              |
| POST   | `/city-fleets/{fleet_id}/condition-reports`                         | Files a new condition report for a vehicle.                              |
| GET    | `/city-fleets/{fleet_id}/condition-reports/{report_id}`             | Retrieves a specific condition report.                                   |
| POST   | `/city-fleets/{fleet_id}/maintenance-holds`                         | Places a maintenance hold on a vehicle.                                  |
| PATCH  | `/city-fleets/{fleet_id}/maintenance-holds/{hold_id}/release`       | Releases an active maintenance hold, removing the `MAINT_HOLD` flag from the vehicle. |
| GET    | `/city-fleets/{fleet_id}/maintenance-holds`                         | Lists maintenance holds, optionally filtered by vehicle.                 |
| GET    | `/city-fleets/{fleet_id}/maintenance-holds/{hold_id}`               | Retrieves a specific maintenance hold.                                   |
| GET    | `/city-fleets/{fleet_id}/ledgers/zone-balance`                      | Retrieves a snapshot of the ZoneBalanceLedger for the entire fleet.      |
| GET    | `/city-fleets/{fleet_id}/ledgers/renter/{renter_id}/reputation`     | Retrieves the reputation ledger data for a specific renter.              |
| GET    | `/city-fleets/{fleet_id}/ledgers/vehicle/{vehicle_id}/mileage`      | Retrieves the mileage ledger data for a specific vehicle.                |

---

#### **Error Code Catalogue**

| Error ID                                  | HTTP Status Code | Notes                                                                                                                              |
| :---------------------------------------- | :--------------- | :--------------------------------------------------------------------------------------------------------------------------------- |
| :---------------------------------------- | :--------------- |
| `ERR_MISSING_OR_INVALID_USER_CONTEXT_HEADER` | 400 Bad Request  |
| `ERR_MISSING_QUOTE_ID`                    | 400 Bad Request  |
| `ERR_FORBIDDEN`                             | 403 Forbidden    | Generic catch-all for unauthorized actions where a more specific 403-level error does not apply (e.g., cross-fleet access attempts). |
| `ERR_USER_SUSPENDED`                        | 403 Forbidden    |
| `ERR_LUXURY_LOCK`                           | 403 Forbidden    |
| `ERR_REPUTATION_LOW`                        | 403 Forbidden    |
| `ERR_PROBATIONARY_RESTRICTION_HIGH_ALERT`   | 403 Forbidden    |
| `ERR_ZONE_NOT_FOUND`                        | 404 Not Found    | Used when any operation references a `zone_id` that does not exist.                                                              |
| `ERR_ACTIVE_RENTAL_EXISTS`                  | 409 Conflict     |
| `ERR_QUOTE_EXISTS`                          | 409 Conflict     |
| `ERR_VEHICLE_NOT_RENTABLE`                  | 409 Conflict     |
| `ERR_VEHICLE_ZONE_MISMATCH`                 | 409 Conflict     |
| `ERR_ZONE_LOCKED_FOR_RENTAL`                | 409 Conflict     |
| `ERR_QUOTE_TAMPER`                          | 409 Conflict     |
| `ERR_RENTAL_NOT_CONFIRMED`                  | 409 Conflict     |
| `ERR_RENTAL_NOT_CANCELLABLE`                | 409 Conflict     |
| `ERR_RENTAL_NOT_ACTIVE`                     | 409 Conflict     |
| `ERR_ZONE_LOCKED_FOR_RETURN`                | 409 Conflict     |
| `ERR_HOLD_NOT_ACTIVE`                       | 409 Conflict     |
| `ERR_VEHICLE_NOT_IN_MAINTENANCE`            | 409 Conflict     |
| `ERR_VEHICLE_NOT_DIRTY`                     | 409 Conflict     |
| `ERR_RENTAL_NOT_IN_REVIEW`                  | 409 Conflict     |
| `ERR_HOLD_NOT_OVERRIDABLE`                  | 409 Conflict     |
| `ERR_AMBIGUOUS_OVERRIDE_HOLD`               | 409 Conflict     |
| `ERR_INVALID_ROLE`                          | 422 Unprocessable |
| `ERR_RESERVED_ZONE_CODE`                    | 422 Unprocessable |
| `ERR_AMBIGUOUS_ZONE_UPDATE`                 | 422 Unprocessable |
| `ERR_CANNOT_MODIFY_NON_RENTER`              | 422 Unprocessable |
| `ERR_INVALID_UPDATE_FIELDS`                 | 422 Unprocessable |
| `ERR_VEHICLE_CLASS_NOT_ALLOWED_IN_FLEET`    | 422 Unprocessable |
| `ERR_VEHICLE_MUST_HAVE_ZONE`                | 422 Unprocessable |
| `ERR_ODOMETER_ROLLBACK`                     | 422 Unprocessable |
| `ERR_VEHICLE_NOT_EV`                        | 422 Unprocessable |
| `ERR_INSUFFICIENT_MAINTENANCE_OVERRIDE_MILES`| 422 Unprocessable |

---
***
