#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Aggregate test results from multiple implementations
 * Usage: node aggregate-test-results.js <test-summaries-dir> <implementation-group-name>
 */

function main() {
  const args = process.argv.slice(2);
  const summariesDir = args[0] || 'test-summaries';
  const groupName = args[1] || 'implementations';
  
  if (!fs.existsSync(summariesDir)) {
    console.error(`Error: Directory ${summariesDir} not found`);
    process.exit(1);
  }
  
  // Try to read run metadata
  let runMetadata = null;
  const metadataPath = path.join('implementations', groupName, 'run-metadata.json');
  if (fs.existsSync(metadataPath)) {
    try {
      runMetadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      console.log('Found run metadata:', runMetadata);
    } catch (error) {
      console.error('Error reading run metadata:', error.message);
    }
  }
  
  // Initialize counters
  const stats = {
    good: {
      count: 0,
      totalTests: 0,
      totalPassed: 0,
      highest: 0,
      implementations: []
    },
    bad: {
      count: 0,
      totalTests: 0,
      totalPassed: 0,
      highest: 0,
      implementations: []
    }
  };
  
  // Initialize per-file statistics tracker
  const fileStats = {};
  
  // Initialize per-test statistics tracker
  const testStats = {};
  let totalImplementations = 0;
  
  // Process each summary directory
  const summaryDirs = fs.readdirSync(summariesDir).filter(dir => 
    fs.statSync(path.join(summariesDir, dir)).isDirectory()
  );
  
  console.log(`Found ${summaryDirs.length} summary directories`);
  
  for (const dir of summaryDirs) {
    const implName = dir.replace('test-summary-', '');
    console.log(`Processing ${implName}...`);
    
    // Find JSON file in directory
    const dirPath = path.join(summariesDir, dir);
    const jsonFiles = fs.readdirSync(dirPath).filter(file => file.endsWith('.json'));
    
    if (jsonFiles.length === 0) {
      console.log(`  No JSON files found in ${dir}`);
      continue;
    }
    
    const jsonFile = path.join(dirPath, jsonFiles[0]);
    console.log(`  Reading ${jsonFile}`);
    
    try {
      const data = JSON.parse(fs.readFileSync(jsonFile, 'utf8'));
      
      // Skip if this was a skipped implementation
      if (data.skipped) {
        console.log(`  Skipping ${implName} (implementation was not generated)`);
        continue;
      }
      
      totalImplementations++;
      
      // Extract data with defaults
      const result = {
        name: implName,
        totalTests: data.totalTests || 0,
        passedTests: data.passedTests || 0,
        failedTests: data.failedTests || 0,
        passPercentage: data.passPercentage || 0
      };
      
      // Process per-file breakdown if available
      if (data.fileBreakdown) {
        for (const [fileName, fileData] of Object.entries(data.fileBreakdown)) {
          if (!fileStats[fileName]) {
            fileStats[fileName] = {
              totalTests: fileData.totalTests || 0,
              implementations: []
            };
          }
          
          // Add this implementation's data for this file
          fileStats[fileName].implementations.push({
            implName: implName,
            passedTests: fileData.passedTests || 0,
            failedTests: fileData.failedTests || 0,
            totalTests: fileData.totalTests || 0
          });
          
          // Track all tests from this file (both passed and failed)
          const failedTestNamesInFile = fileData.failedTestNames || [];
          const passedTestNamesInFile = fileData.passedTestNames || [];
          
          // Track failed tests
          failedTestNamesInFile.forEach(testName => {
            const uniqueTestName = `[${fileName}] ${testName}`;
            if (!testStats[uniqueTestName]) {
              testStats[uniqueTestName] = {
                failCount: 0,
                passCount: 0,
                totalRuns: 0,
                fileName: fileName,
                originalTestName: testName
              };
            }
            testStats[uniqueTestName].failCount++;
            testStats[uniqueTestName].totalRuns++;
          });
          
          // Track passed tests
          passedTestNamesInFile.forEach(testName => {
            const uniqueTestName = `[${fileName}] ${testName}`;
            if (!testStats[uniqueTestName]) {
              testStats[uniqueTestName] = {
                failCount: 0,
                passCount: 0,
                totalRuns: 0,
                fileName: fileName,
                originalTestName: testName
              };
            }
            testStats[uniqueTestName].passCount++;
            testStats[uniqueTestName].totalRuns++;
          });
        }
      }
      
      // Categorize implementation
      const isGood = implName.includes('good-implementation');
      const category = isGood ? stats.good : stats.bad;
      
      category.count++;
      category.totalTests += result.totalTests;
      category.totalPassed += result.passedTests;
      category.highest = Math.max(category.highest, result.passPercentage);
      category.implementations.push(result);
      
      console.log(`  Tests: ${result.totalTests}, Passed: ${result.passedTests}, Rate: ${result.passPercentage}%`);
      
    } catch (error) {
      console.error(`  Error reading ${jsonFile}: ${error.message}`);
    }
  }
  
  // Calculate per-file averages
  const fileAverages = calculateFileAverages(fileStats);
  
  // Calculate test failure and success statistics
  const testFailureStats = calculateTestFailureStats(testStats);
  const testSuccessStats = calculateTestSuccessStats(testStats);
  
  // Generate markdown report
  const report = generateMarkdownReport(groupName, stats, fileAverages, testFailureStats, testSuccessStats, runMetadata);
  
  // Sort implementations for JSON output
  const allImplementations = [
    ...stats.good.implementations.sort((a, b) => a.name.localeCompare(b.name)),
    ...stats.bad.implementations.sort((a, b) => a.name.localeCompare(b.name))
  ];
  
  // Write reports
  fs.writeFileSync('aggregate-report.md', report);
  console.log('\nReport written to aggregate-report.md');
  
  // Also write JSON summary for programmatic access
  const jsonSummary = {
    groupName,
    generatedAt: new Date().toISOString(),
    runMetadata: runMetadata,
    good: {
      count: stats.good.count,
      averagePassRate: stats.good.totalTests > 0 
        ? Math.round((stats.good.totalPassed / stats.good.totalTests) * 100)
        : 0,
      highestPassRate: stats.good.highest,
      totalTests: stats.good.totalTests,
      totalPassed: stats.good.totalPassed
    },
    bad: {
      count: stats.bad.count,
      averagePassRate: stats.bad.totalTests > 0 
        ? Math.round((stats.bad.totalPassed / stats.bad.totalTests) * 100)
        : 0,
      highestPassRate: stats.bad.highest,
      totalTests: stats.bad.totalTests,
      totalPassed: stats.bad.totalPassed
    },
    implementations: allImplementations,
    fileAverages: fileAverages,
    testFailureStats: testFailureStats,
    testSuccessStats: testSuccessStats
  };
  
  fs.writeFileSync('aggregate-report.json', JSON.stringify(jsonSummary, null, 2));
  console.log('JSON summary written to aggregate-report.json');
  
  // Also output to console for GitHub Actions
  console.log('\n=== AGGREGATE REPORT ===\n');
  console.log(report);
}

function calculateFileAverages(fileStats) {
  const averages = [];
  
  for (const [fileName, data] of Object.entries(fileStats)) {
    let totalFailedTests = 0;
    let totalPassedTests = 0;
    let implementationCount = 0;
    
    for (const impl of data.implementations) {
      totalFailedTests += impl.failedTests;
      totalPassedTests += impl.passedTests;
      implementationCount++;
    }
    
    const avgFailedTests = implementationCount > 0 
      ? totalFailedTests / implementationCount 
      : 0;
    
    const avgPassRate = implementationCount > 0 && data.totalTests > 0
      ? Math.round((totalPassedTests / (implementationCount * data.totalTests)) * 100)
      : 0;
    
    averages.push({
      fileName: fileName,
      totalTests: data.totalTests,
      avgFailedTests: avgFailedTests,
      avgPassRate: avgPassRate
    });
  }
  
  // Sort by pass rate (highest to lowest)
  return averages.sort((a, b) => b.avgPassRate - a.avgPassRate);
}

function calculateTestFailureStats(testStats) {
  const failureStats = [];
  
  for (const [testName, data] of Object.entries(testStats)) {
    if (data.totalRuns > 0) {
      const failPercentage = Math.round((data.failCount / data.totalRuns) * 100);
      
      // Only include tests that failed more than 75% of the time
      if (failPercentage >= 75) {
        failureStats.push({
          testName: testName,
          failPercentage: failPercentage,
          failCount: data.failCount,
          totalRuns: data.totalRuns,
          fileName: data.fileName,
          originalTestName: data.originalTestName
        });
      }
    }
  }
  
  // Sort by fail percentage (highest first)
  return failureStats.sort((a, b) => b.failPercentage - a.failPercentage);
}

function calculateTestSuccessStats(testStats) {
  const successStats = [];
  
  // For each test, calculate success rate
  for (const [testName, data] of Object.entries(testStats)) {
    if (data.totalRuns > 0) {
      const passPercentage = Math.round((data.passCount / data.totalRuns) * 100);
      
      // Only include tests that passed more than 75% of the time
      if (passPercentage >= 75) {
        successStats.push({
          testName: testName,
          passPercentage: passPercentage,
          passCount: data.passCount,
          totalRuns: data.totalRuns,
          fileName: data.fileName,
          originalTestName: data.originalTestName
        });
      }
    }
  }
  
  // Sort by pass percentage (highest first)
  return successStats.sort((a, b) => b.passPercentage - a.passPercentage);
}

function generateMarkdownReport(groupName, stats, fileAverages, testFailureStats, testSuccessStats, runMetadata) {
  let md = `# 📊 Test Results Report for ${groupName}\n\n`;
  
  // Add run metadata if available
  if (runMetadata) {
    md += `## 🔧 Run Configuration\n\n`;
    md += `- **Model Used:** ${runMetadata.model}\n`;
    md += `- **Created At:** ${new Date(runMetadata.createdAt).toUTCString()}\n`;
    md += `- **Total Implementations:** ${runMetadata.implementationCount.total} (${runMetadata.implementationCount.good} good, ${runMetadata.implementationCount.bad} bad)\n\n`;
  }
  
  md += `Generated on: ${new Date().toUTCString()}\n\n`;
  
  // Implementation summary table
  md += `## Implementation Summary\n\n`;
  md += `| Implementation | Total Tests | Passed | Failed | Pass Rate | Status |\n`;
  md += `|----------------|-------------|--------|--------|-----------|--------|\n`;
  
  // Sort implementations for consistent output
  const allImplementations = [
    ...stats.good.implementations.sort((a, b) => a.name.localeCompare(b.name)),
    ...stats.bad.implementations.sort((a, b) => a.name.localeCompare(b.name))
  ];
  
  for (const impl of allImplementations) {
    const status = impl.passPercentage >= 75 ? '🔴' :
                   impl.passPercentage >= 50 ? '🟡' : '🟢';
    
    md += `| ${impl.name} | ${impl.totalTests} | ${impl.passedTests} | ${impl.failedTests} | ${impl.passPercentage}% | ${status} |\n`;
  }
  
  md += '\n';
  
  // Good implementations statistics
  if (stats.good.count > 0) {
    md += `## 📊 Overall Statistics - Good Implementations\n\n`;
    
    const avgPassRate = stats.good.totalTests > 0 
      ? Math.round((stats.good.totalPassed / stats.good.totalTests) * 100)
      : 0;
    
    md += `- **Average Pass Rate:** ${avgPassRate}%\n`;
    md += `- **Highest Pass Rate:** ${stats.good.highest}%\n\n`;
  }
  
  // Bad implementations statistics
  if (stats.bad.count > 0) {
    md += `## 📊 Overall Statistics - Bad Implementations\n\n`;
    
    const avgPassRate = stats.bad.totalTests > 0 
      ? Math.round((stats.bad.totalPassed / stats.bad.totalTests) * 100)
      : 0;
    
    md += `- **Average Pass Rate:** ${avgPassRate}%\n`;
    md += `- **Highest Pass Rate:** ${stats.bad.highest}%\n\n`;
  }
  
  // Per-file statistics
  if (fileAverages.length > 0) {
    md += `## 📁 Per-Test-File Statistics\n\n`;
    md += `| Test File | Tests | Avg Failed | Avg Pass Rate |\n`;
    md += `|-----------|-------|------------|---------------|\n`;
    
    for (const file of fileAverages) {
      const emoji = file.avgPassRate >= 75 ? '🔴' : file.avgPassRate >= 50 ? '🟡' : '🟢';
      md += `| ${file.fileName} | ${file.totalTests} | ${file.avgFailedTests.toFixed(1)} | ${file.avgPassRate}% ${emoji} |\n`;
    }
    
    md += '\n';
  }
  
  // High-success tests
  if (testSuccessStats.length > 0) {
    md += `## ✅ Passed Tests Breakdown (>=75% pass rate)\n\n`;
    md += `| Test Name | Pass Rate |\n`;
    md += `|-----------|----------|\n`;
    
    for (const test of testSuccessStats) {
      md += `| ${test.testName} | ${test.passPercentage}% |\n`;
    }
    
    md += '\n';
    md += `*Showing tests that passed in more than 75% of implementations*\n\n`;
  }
  
  // High-failure tests
  if (testFailureStats.length > 0) {
    md += `## 🚨 Failed Tests Breakdown (>=75% failure rate)\n\n`;
    md += `| Test Name | Fail Rate |\n`;
    md += `|-----------|----------|\n`;
    
    for (const test of testFailureStats) {
      md += `| ${test.testName} | ${test.failPercentage}% |\n`;
    }
    
    md += '\n';
    md += `*Note: Only showing tests that failed in more than 75% of implementations*\n\n`;
  }
  
  return md;
}

// Run the script
if (require.main === module) {
  main();
} 
